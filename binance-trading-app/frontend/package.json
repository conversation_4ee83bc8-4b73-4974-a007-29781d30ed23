{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@pinia/nuxt": "^0.11.1", "lightweight-charts": "^5.0.7", "pinia": "^3.0.3", "vue": "^3.2.37", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/types": "^7.18.10", "@vitejs/plugin-vue": "^3.0.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.5", "tailwindcss": "^4.1.10", "typescript": "^4.6.4", "vite": "^3.0.7", "vue-tsc": "^1.8.27"}}