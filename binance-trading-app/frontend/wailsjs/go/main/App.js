// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function CancelOrder(arg1, arg2) {
  return window['go']['main']['App']['CancelOrder'](arg1, arg2);
}

export function CreateOrder(arg1, arg2, arg3, arg4, arg5) {
  return window['go']['main']['App']['CreateOrder'](arg1, arg2, arg3, arg4, arg5);
}

export function DeleteAPIKey(arg1) {
  return window['go']['main']['App']['DeleteAPIKey'](arg1);
}

export function GetAPIKey(arg1) {
  return window['go']['main']['App']['GetAPIKey'](arg1);
}

export function GetAccountInfo() {
  return window['go']['main']['App']['GetAccountInfo']();
}

export function GetAvailableSymbols() {
  return window['go']['main']['App']['GetAvailableSymbols']();
}

export function GetBalance(arg1) {
  return window['go']['main']['App']['GetBalance'](arg1);
}

export function GetKlines(arg1, arg2, arg3) {
  return window['go']['main']['App']['GetKlines'](arg1, arg2, arg3);
}

export function GetOpenOrders(arg1) {
  return window['go']['main']['App']['GetOpenOrders'](arg1);
}

export function GetOrderBook(arg1, arg2) {
  return window['go']['main']['App']['GetOrderBook'](arg1, arg2);
}

export function GetStoredKeys() {
  return window['go']['main']['App']['GetStoredKeys']();
}

export function GetTicker(arg1) {
  return window['go']['main']['App']['GetTicker'](arg1);
}

export function ListAPIKeys() {
  return window['go']['main']['App']['ListAPIKeys']();
}

export function SaveAPIKey(arg1, arg2, arg3, arg4) {
  return window['go']['main']['App']['SaveAPIKey'](arg1, arg2, arg3, arg4);
}

export function SetActiveAPIKey(arg1) {
  return window['go']['main']['App']['SetActiveAPIKey'](arg1);
}

export function SetBinanceAPIKeys(arg1, arg2) {
  return window['go']['main']['App']['SetBinanceAPIKeys'](arg1, arg2);
}
