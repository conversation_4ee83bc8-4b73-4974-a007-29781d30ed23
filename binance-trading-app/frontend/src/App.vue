<template>
  <div class="app-container">
    <!-- Router view will render the matched component -->
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    
    <!-- Global notification -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      {{ notification.message }}
      <button @click="hideNotification" class="close-btn">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTradingStore } from './stores/trading'

const router = useRouter()
const store = useTradingStore()

// Global notification
const notification = ref({
  show: false,
  message: '',
  type: 'info' // 'info', 'success', 'warning', 'error'
})

let notificationTimeout: number | null = null

// Show notification
const showNotification = (message: string, type = 'info', duration = 5000) => {
  // Clear any existing timeout
  if (notificationTimeout) {
    clearTimeout(notificationTimeout)
    notificationTimeout = null
  }
  
  // Set new notification
  notification.value = {
    show: true,
    message,
    type
  }
  
  // Auto-hide after duration
  notificationTimeout = window.setTimeout(() => {
    hideNotification()
  }, duration)
}

// Hide notification
const hideNotification = () => {
  notification.value.show = false
  if (notificationTimeout) {
    clearTimeout(notificationTimeout)
    notificationTimeout = null
  }
}

// Listen for global errors
const handleGlobalError = (event: ErrorEvent) => {
  console.error('Global error:', event.error)
  showNotification(event.error.message || 'An unexpected error occurred', 'error')
}

// Listen for unhandled promise rejections
const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
  console.error('Unhandled rejection:', event.reason)
  showNotification(event.reason?.message || 'An unexpected error occurred', 'error')
}

// Set up global error handlers
onMounted(() => {
  window.addEventListener('error', handleGlobalError)
  window.addEventListener('unhandledrejection', handleUnhandledRejection)
  
  // Expose showNotification globally for use in components
  window.showNotification = showNotification
})

// Clean up event listeners
onUnmounted(() => {
  window.removeEventListener('error', handleGlobalError)
  window.removeEventListener('unhandledrejection', handleUnhandledRejection)
  
  if (notificationTimeout) {
    clearTimeout(notificationTimeout)
  }
  
  // Clean up global function
  if (window.showNotification) {
    delete window.showNotification
  }
})

// Make showNotification available to templates
const expose = {
  showNotification,
  hideNotification
}

defineExpose(expose)
</script>

<style>
/* Base styles */
:root {
  --color-primary: #3B82F6;
  --color-primary-dark: #2563EB;
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-text: #1F2937;
  --color-text-light: #6B7280;
  --color-bg: #F9FAFB;
  --color-card: #FFFFFF;
  --color-border: #E5E7EB;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --radius-sm: 0.25rem;
  --radius: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --transition: all 0.15s ease-in-out;
}

/* Dark mode */
.dark {
  --color-text: #F9FAFB;
  --color-text-light: #9CA3AF;
  --color-bg: #111827;
  --color-card: #1F2937;
  --color-border: #374151;
}

/* Base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  min-height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-bg);
  transition: background-color 0.2s, color 0.2s;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  margin-bottom: 0.5em;
  color: var(--color-text);
}

h1 { font-size: 2rem; }
h2 { font-size: 1.5rem; }
h3 { font-size: 1.25rem; }

p {
  margin-bottom: 1rem;
  color: var(--color-text-light);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s;
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* Buttons */
button {
  font-family: inherit;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: var(--radius);
  padding: 0.5rem 1rem;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Forms */
input, select, textarea {
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  padding: 0.5rem 0.75rem;
  width: 100%;
  transition: var(--transition);
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Cards */
.card {
  background-color: var(--color-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--color-border);
}

/* Notifications */
.notification {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  padding: 1rem 1.5rem;
  border-radius: var(--radius);
  color: white;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  max-width: 24rem;
  z-index: 50;
  box-shadow: var(--shadow-lg);
  animation: slideIn 0.3s ease-out forwards;
}

.notification.info {
  background-color: var(--color-primary);
}

.notification.success {
  background-color: var(--color-success);
}

.notification.warning {
  background-color: var(--color-warning);
}

.notification.error {
  background-color: var(--color-error);
}

.notification .close-btn {
  margin-left: auto;
  padding: 0.25rem;
  border-radius: 9999px;
  color: white;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.notification .close-btn:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

@keyframes slideIn {
  from {
    transform: translateY(1rem);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.15s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-light);
}

/* Utilities */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

/* Dark mode toggle */
.dark-mode-toggle {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  z-index: 40;
  background-color: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: 9999px;
  padding: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  box-shadow: var(--shadow);
}

.dark-mode-toggle:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.dark-mode-toggle:active {
  transform: translateY(0);
}

/* Responsive helpers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Print styles */
@media print {
  body {
    background: none;
    color: #000;
  }
  
  .no-print {
    display: none !important;
  }
}
</style>
