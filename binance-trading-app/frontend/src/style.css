@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply bg-gray-900 text-white;
  }
  
  body {
    @apply m-0 font-sans;
  }
  
  #app {
    @apply h-screen;
  }
}

@font-face {
  font-family: "Nunito";
  font-style: normal;
  font-weight: 400;
  src: local(""),
    url("assets/fonts/nunito-v16-latin-regular.woff2") format("woff2");
}

:root {
  --color-buy: #00c853;
  --color-sell: #ff5252;
  --color-buy-bg: rgba(0, 200, 83, 0.12);
  --color-sell-bg: rgba(255, 82, 82, 0.12);
  --color-buy-hover: rgba(0, 200, 83, 0.2);
  --color-sell-hover: rgba(255, 82, 82, 0.2);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  @apply w-1.5 h-1.5;
}

::-webkit-scrollbar-track {
  @apply bg-slate-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-slate-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-slate-500;
}

/* Animations */
@keyframes flash-buy {
  0% { background-color: var(--color-buy-bg); }
  100% { background-color: transparent; }
}

@keyframes flash-sell {
  0% { background-color: var(--color-sell-bg); }
  100% { background-color: transparent; }
}

.flash-buy {
  animation: flash-buy 0.5s ease-out;
}

.flash-sell {
  animation: flash-sell 0.5s ease-out;
}
