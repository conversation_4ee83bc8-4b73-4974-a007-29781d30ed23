// Wails runtime types
declare global {
  interface Window {
    go: {
      main: {
        App: {
          // API Key management
          SetBinanceAPIKeys(apiKey: string, apiSecret: string): Promise<void>
          SaveAPIKey(name: string, apiKey: string, apiSecret: string, isTestnet: boolean): Promise<void>
          GetAPIKey(name: string): Promise<any>
          ListAPIKeys(): Promise<any[]>
          DeleteAPIKey(name: string): Promise<void>
          SetActiveAPIKey(name: string): Promise<void>
          GetStoredKeys(): Promise<any>

          // Account info
          GetAccountInfo(): Promise<any>
          GetBalance(asset: string): Promise<any>
          
          // Market data
          GetTicker(symbol: string): Promise<any>
          GetAvailableSymbols(): Promise<string[]>
          GetKlines(symbol: string, interval: string, limit: number): Promise<any[]>
          GetOrderBook(symbol: string, limit: number): Promise<any>
          
          // Trading
          CreateOrder(symbol: string, side: string, orderType: string, quantity: number, price: number): Promise<any>
          GetOpenOrders(symbol: string): Promise<any[]>
          CancelOrder(orderID: string, symbol: string): Promise<void>
        }
      }
    }
    
    // Global notification function (optional)
    showNotification?: (message: string, type?: string, duration?: number) => void
  }
}

export {}
