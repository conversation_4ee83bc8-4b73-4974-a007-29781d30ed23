<template>
  <div class="connection-view">
    <div class="connection-container">
      <div class="logo">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-12 h-12 text-indigo-500">
          <path d="M10.5 1.875a1.125 1.125 0 012.25 0v8.219c.517.162 1.02.382 1.5.659V3.375a1.125 1.125 0 012.25 0v10.937a4.505 4.505 0 00-3.25 3.706 75.27 75.27 0 00-3.722-3.772 4.5 4.5 0 00-5.44 0 75.27 75.27 0 00-3.722 3.772 4.5 4.5 0 10-1.5 1.615 77.767 77.767 0 013.904-3.905 75.68 75.68 0 017.439-5.659V15a.75.75 0 001.5 0V6.713a75.75 75.75 0 013.5 5.707v9.33a1.5 1.5 0 11-3 0V14.25a.75.75 0 011.5 0v2.25a3 3 0 003 3h3a3 3 0 003-3v-9.129a9.007 9.007 0 01-1.5.656V15a.75.75 0 01-1.5 0V6.98a75.39 75.39 0 01-1.5-.615v9.02a4.5 4.5 0 11-9 0V1.875z" />
        </svg>
        <h1>Binance Trading App</h1>
      </div>

      <div class="connection-card">
        <h2>Connect to Binance</h2>
        <p class="description">
          Enter your API credentials to connect. Your keys are stored securely and never leave your device.
          <a href="https://www.binance.com/en/support/faq/360002502072" target="_blank" class="help-link">
            How to get API keys?
          </a>
        </p>
        
        <div v-if="error" class="error-message">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          {{ error }}
        </div>
        
        <form @submit.prevent="handleSubmit" class="connection-form">
          <div class="form-group">
            <label for="apiKey">API Key</label>
            <div class="input-wrapper">
              <input
                id="apiKey"
                v-model="apiKey"
                type="password"
                placeholder="Enter your Binance API Key"
                autocomplete="off"
                :disabled="isConnecting"
              />
            </div>
          </div>
          
          <div class="form-group">
            <label for="apiSecret">API Secret</label>
            <div class="input-wrapper">
              <input
                id="apiSecret"
                v-model="apiSecret"
                type="password"
                placeholder="Enter your Binance API Secret"
                autocomplete="off"
                :disabled="isConnecting"
              />
            </div>
          </div>
          
          <div class="form-actions">
            <button 
              type="submit"
              :disabled="isConnecting || !isFormValid"
              class="connect-btn"
            >
              <span v-if="isConnecting" class="spinner"></span>
              {{ isConnecting ? 'Connecting...' : 'Connect to Binance' }}
            </button>
            
            <button 
              type="button"
              @click="useTestMode"
              :disabled="isConnecting"
              class="test-mode-btn"
            >
              Try Test Mode
            </button>
          </div>
        </form>
        
        <div class="disclaimer">
          <p>By connecting, you agree to our <a href="#" class="link">Terms of Service</a> and <a href="#" class="link">Privacy Policy</a>.</p>
          <p>Test mode uses real market data but does not place real orders.</p>
        </div>
      </div>
      
      <div class="footer">
        <p>© 2023 Binance Trading App. Not affiliated with Binance.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTradingStore } from '@/stores/trading'

const router = useRouter()
const store = useTradingStore()

// Form state
const apiKey = ref('')
const apiSecret = ref('')
const isConnecting = ref(false)
const error = ref('')

// Computed
const isFormValid = computed(() => {
  return apiKey.value.trim() !== '' && apiSecret.value.trim() !== ''
})

// Methods
async function handleSubmit() {
  if (!isFormValid.value) return
  
  isConnecting.value = true
  error.value = ''

  try {
    // Store the API keys
    await window.go.main.App.SetBinanceAPIKeys(apiKey.value, apiSecret.value)

    // Try to connect to Binance
    const accountInfo = await window.go.main.App.GetAccountInfo()
    
    if (accountInfo) {
      // Update store
      store.setConnectionStatus(true)
      store.setAccountInfo(accountInfo)
      
      // Redirect to trading view
      router.push('/trading')
    }
  } catch (err) {
    console.error('Connection error:', err)
    error.value = (err as Error).message || 'Failed to connect to Binance. Please check your API keys and try again.'
  } finally {
    isConnecting.value = false
  }
}

async function useTestMode() {
  isConnecting.value = true
  error.value = ''
  
  try {
    // Clear any stored API keys
    await window.go.main.App.SetBinanceAPIKeys('', '')
    
    // Update store for test mode
    store.setConnectionStatus(true)
    store.setIsTestMode(true)
    
    // Redirect to trading view
    router.push('/trading')
  } catch (err) {
    console.error('Error entering test mode:', err)
    error.value = 'Failed to enter test mode. Please try again.'
  } finally {
    isConnecting.value = false
  }
}

// Load stored API keys on mount
onMounted(async () => {
  try {
    const keys = await window.go.main.App.GetStoredKeys()
    if (keys) {
      apiKey.value = keys.apiKey || ''
      apiSecret.value = keys.apiSecret || ''
      
      // Auto-connect if we have stored keys
      if (apiKey.value && apiSecret.value) {
        handleSubmit()
      }
    }
  } catch (err) {
    console.error('Error loading stored keys:', err)
  }
})
</script>

<style scoped>
.connection-view {
  @apply min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4;
}

.connection-container {
  @apply w-full max-w-md space-y-8;
}

.logo {
  @apply text-center space-y-2 mb-8;
}

.logo svg {
  @apply mx-auto h-16 w-16 text-indigo-600 dark:text-indigo-400;
}

.logo h1 {
  @apply text-2xl font-bold text-gray-900 dark:text-white;
}

.connection-card {
  @apply bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8 space-y-6;
  @apply border border-gray-200 dark:border-gray-700;
}

h2 {
  @apply text-2xl font-bold text-center text-gray-900 dark:text-white;
}

.description {
  @apply text-sm text-gray-600 dark:text-gray-400 text-center;
}

.help-link {
  @apply text-indigo-600 dark:text-indigo-400 hover:underline;
}

.error-message {
  @apply bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-3 rounded-md text-sm flex items-start space-x-2;
}

.error-message svg {
  @apply flex-shrink-0 h-5 w-5;
}

.form-group {
  @apply space-y-1;
}

label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.input-wrapper {
  @apply relative rounded-md shadow-sm;
}

input {
  @apply block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white sm:text-sm;
  @apply disabled:opacity-50 disabled:bg-gray-50 disabled:text-gray-500 dark:disabled:bg-gray-700 dark:disabled:text-gray-400;
  @apply px-3 py-2 border;
}

.form-actions {
  @apply space-y-3 pt-2;
}

button {
  @apply w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
  @apply transition-colors duration-150;
}

.connect-btn {
  @apply bg-indigo-600 hover:bg-indigo-700 text-white;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.test-mode-btn {
  @apply bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600;
}

.spinner {
  @apply animate-spin -ml-1 mr-2 h-5 w-5 text-white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  display: inline-block;
}

.disclaimer {
  @apply text-xs text-gray-500 dark:text-gray-400 text-center space-y-1;
}

.disclaimer .link {
  @apply text-indigo-600 dark:text-indigo-400 hover:underline;
}

.footer {
  @apply text-center text-xs text-gray-500 dark:text-gray-400 mt-8;
}

/* Dark mode overrides */
.dark .connection-card {
  @apply bg-gray-800 border-gray-700;
}

dark .input-wrapper input {
  @apply bg-gray-700 border-gray-600 text-white;
}

dark .test-mode-btn {
  @apply bg-gray-700 text-gray-200 border-gray-600;
}

dark .connect-btn {
  @apply bg-indigo-600 hover:bg-indigo-700;
}
</style>
