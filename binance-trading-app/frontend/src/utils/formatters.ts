/**
 * Format a number with specified decimal places
 */
export function formatNumber(value: number | string, decimals: number = 2): string {
  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return '0.00'
  return num.toFixed(decimals)
}

/**
 * Format a price with appropriate decimal places
 */
export function formatPrice(value: number | string): string {
  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return '0.00'
  
  if (num >= 1000) {
    return num.toFixed(2)
  } else if (num >= 1) {
    return num.toFixed(4)
  } else {
    return num.toFixed(8)
  }
}

/**
 * Format a percentage value
 */
export function formatPercent(value: number | string, decimals: number = 2): string {
  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return '0.00%'
  return `${num.toFixed(decimals)}%`
}

/**
 * Format a large number with K, M, B suffixes
 */
export function formatLargeNumber(value: number | string): string {
  const num = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(num)) return '0'
  
  if (num >= 1e9) {
    return `${(num / 1e9).toFixed(2)}B`
  } else if (num >= 1e6) {
    return `${(num / 1e6).toFixed(2)}M`
  } else if (num >= 1e3) {
    return `${(num / 1e3).toFixed(2)}K`
  } else {
    return num.toFixed(2)
  }
}

/**
 * Format volume with appropriate units
 */
export function formatVolume(value: number | string): string {
  return formatLargeNumber(value)
}
