<template>
  <div class="order-book">
    <div class="order-book-header">
      <h3>Order Book</h3>
      <div class="spread" v-if="midPrice">
        Spread: {{ spreadPercentage }}%
      </div>
    </div>
    
    <div class="order-book-container">
      <!-- Asks (Sell orders) -->
      <div class="order-book-side asks">
        <div class="order-book-row header">
          <div>Price (USDT)</div>
          <div>Size (BTC)</div>
          <div>Total</div>
        </div>
        <div 
          v-for="(ask, index) in asks" 
          :key="`ask-${index}`"
          class="order-book-row ask"
          :style="{ '--depth': getAskDepth(ask) }"
        >
          <div class="price">{{ formatPrice(ask.price) }}</div>
          <div>{{ formatSize(ask.quantity) }}</div>
          <div>{{ formatSize(calculateTotal(ask, 'ask')) }}</div>
        </div>
      </div>
      
      <!-- Mid price -->
      <div class="mid-price" v-if="midPrice">
        {{ formatPrice(midPrice) }}
        <div class="mid-price-label">Price</div>
      </div>
      
      <!-- Bids (Buy orders) -->
      <div class="order-book-side bids">
        <div 
          v-for="(bid, index) in bids" 
          :key="`bid-${index}`"
          class="order-book-row bid"
          :style="{ '--depth': getBidDepth(bid) }"
        >
          <div class="price">{{ formatPrice(bid.price) }}</div>
          <div>{{ formatSize(bid.quantity) }}</div>
          <div>{{ formatSize(calculateTotal(bid, 'bid')) }}</div>
        </div>
        <div class="order-book-row header">
          <div>Price (USDT)</div>
          <div>Size (BTC)</div>
          <div>Total</div>
        </div>
      </div>
    </div>
    
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
    
    <div v-if="error" class="error-message">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useTradingStore } from '@/stores/trading'

const store = useTradingStore()
const isLoading = ref(false)
const error = ref<string | null>(null)

// Computed properties
const orderBook = computed(() => store.orderBook)
const midPrice = computed(() => store.midPrice)

// Get bids and asks with limits
const bids = computed(() => {
  if (!orderBook.value) return []
  return orderBook.value.bids.slice(0, 20) // Show top 20 bids
})

const asks = computed(() => {
  if (!orderBook.value) return []
  // Show top 20 asks (reversed to show from lowest to highest)
  return [...orderBook.value.asks].slice(0, 20).reverse()
})

// Calculate spread percentage
const spreadPercentage = computed(() => {
  if (!orderBook.value || !orderBook.value.bids.length || !orderBook.value.asks.length) return '0.00'
  
  const bestBid = parseFloat(orderBook.value.bids[0].price)
  const bestAsk = parseFloat(orderBook.value.asks[0].price)
  const spread = bestAsk - bestBid
  const spreadPct = (spread / bestBid) * 100
  
  return spreadPct.toFixed(2)
})

// Calculate total for depth visualization
const calculateTotal = (entry: { price: string; quantity: string }, side: 'bid' | 'ask') => {
  if (!orderBook.value) return '0'
  
  const entries = side === 'bid' ? orderBook.value.bids : orderBook.value.asks
  const price = parseFloat(entry.price)
  const quantity = parseFloat(entry.quantity)
  
  let total = 0
  for (const e of entries) {
    const ePrice = parseFloat(e.price)
    const eQty = parseFloat(e.quantity)
    
    if (side === 'bid' ? ePrice >= price : ePrice <= price) {
      total += eQty * ePrice
    } else {
      break
    }
  }
  
  return total.toString()
}

// Format price with proper decimal places
const formatPrice = (price: string) => {
  const num = parseFloat(price)
  if (num >= 1000) return num.toFixed(2)
  if (num >= 1) return num.toFixed(4)
  return num.toFixed(8).replace(/\.?0+$/, '') // Remove trailing zeros after decimal
}

// Format size with proper decimal places
const formatSize = (size: string) => {
  const num = parseFloat(size)
  if (num >= 1) return num.toFixed(4)
  return num.toFixed(8).replace(/\.?0+$/, '') // Remove trailing zeros after decimal
}

// Calculate depth for visualization (0-100%)
const getBidDepth = (bid: { price: string; quantity: string }) => {
  if (!orderBook.value || !orderBook.value.bids.length) return '0%'
  
  const maxBidQty = Math.max(...orderBook.value.bids.map((b: any) => parseFloat(b.quantity)))
  const bidQty = parseFloat(bid.quantity)
  return `${Math.min(100, (bidQty / maxBidQty) * 100)}%`
}

const getAskDepth = (ask: { price: string; quantity: string }) => {
  if (!orderBook.value || !orderBook.value.asks.length) return '0%'

  const maxAskQty = Math.max(...orderBook.value.asks.map((a: any) => parseFloat(a.quantity)))
  const askQty = parseFloat(ask.quantity)
  return `${Math.min(100, (askQty / maxAskQty) * 100)}%`
}

// Load order book data
const loadOrderBook = async () => {
  if (!store.selectedSymbol) return

  try {
    isLoading.value = true
    error.value = null

    // Get order book data from backend
    const orderBookData = await window.go.main.App.GetOrderBook(store.selectedSymbol, 20)

    if (orderBookData) {
      store.updateOrderBook(store.selectedSymbol, orderBookData)
    }
  } catch (err) {
    console.error('Error loading order book:', err)
    error.value = 'Failed to load order book data'
  } finally {
    isLoading.value = false
  }
}

// Auto-refresh data
let refreshInterval: number | null = null

const startAutoRefresh = () => {
  stopAutoRefresh()
  refreshInterval = setInterval(() => {
    loadOrderBook()
  }, 5000) // Refresh every 5 seconds
}

const stopAutoRefresh = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
}

// Lifecycle hooks
onMounted(() => {
  loadOrderBook()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.order-book {
  background-color: #1E293B;
  border-radius: 0.5rem;
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.order-book-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #334155;
}

.order-book-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #E2E8F0;
}

.spread {
  font-size: 0.75rem;
  color: #94A3B8;
}

.order-book-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.order-book-side {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #334155 #1E293B;
}

.order-book-side.asks {
  margin-bottom: 0.5rem;
}

.order-book-side.bids {
  margin-top: 0.5rem;
}

.order-book-row {
  display: flex;
  justify-content: space-between;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  position: relative;
  z-index: 1;
}

.order-book-row.header {
  color: #94A3B8;
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  background-color: #0F172A;
  position: sticky;
  top: 0;
  z-index: 2;
}

.order-book-row > div {
  flex: 1;
  text-align: right;
}

.order-book-row > div:first-child {
  text-align: left;
}

.bid .price {
  color: #10B981;
}

.ask .price {
  color: #EF4444;
}

.bid::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(16, 185, 129, 0.1);
  width: var(--depth, '0%');
  z-index: -1;
  transition: width 0.3s ease;
}

.ask::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(239, 68, 68, 0.1);
  width: var(--depth, '0%');
  z-index: -1;
  transition: width 0.3s ease;
  margin-left: auto;
}

.mid-price {
  text-align: center;
  padding: 0.5rem;
  font-size: 1.25rem;
  font-weight: 500;
  color: #E2E8F0;
  background-color: #0F172A;
  border-radius: 0.25rem;
  margin: 0.25rem 0;
  position: relative;
  z-index: 2;
}

.mid-price-label {
  font-size: 0.7rem;
  color: #94A3B8;
  margin-top: 0.1rem;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(15, 23, 42, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 0.2rem solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: #3B82F6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: #FEE2E2;
  color: #B91C1C;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  z-index: 20;
}
</style>
