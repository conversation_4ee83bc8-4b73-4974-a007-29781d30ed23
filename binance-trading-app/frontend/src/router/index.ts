import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { useTradingStore } from '@/stores/trading'

// Lazy load components for better performance
const ConnectionView = () => import('@/views/ConnectionView.vue')
const TradingView = () => import('@/views/TradingView.vue')

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Connection',
    component: ConnectionView,
    meta: { requiresAuth: false }
  },
  {
    path: '/trading',
    name: 'Trading',
    component: TradingView,
    meta: { requiresAuth: true }
  },
  // Redirect to home page if route doesn't exist
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior() {
    // Always scroll to top when navigating
    return { top: 0 }
  }
})

// Navigation guard to handle authentication
router.beforeEach((to, from, next) => {
  const store = useTradingStore()
  
  // If the route requires authentication and user is not connected
  if (to.meta.requiresAuth && !store.isConnected) {
    // Redirect to login page with the return url
    return next({ 
      path: '/', 
      query: { 
        redirect: to.fullPath 
      } 
    })
  }

  // If user is connected and tries to access login page
  if (to.path === '/' && store.isConnected) {
    return next('/trading')
  }

  next()
})

export default router
