package binance

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2"
)

// Client is a wrapper around the Binance API client that implements the Broker interface
type Client struct {
	client   *binance.Client
	config   *Config
	testMode bool
	mu       sync.RWMutex
}

// NewClient creates a new Binance client
func NewClient(config *Config) *Client {
	client := binance.NewClient(config.APIKey, config.APISecret)
	
	// Set testnet if needed
	if config.TestNet {
		binance.UseTestnet = true
	}

	return &Client{
		client:   client,
		config:   config,
		testMode: config.TestMode,
	}
}

// SetTestMode enables or disables test mode
func (c *Client) SetTestMode(enabled bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.testMode = enabled
}

// IsTestMode returns true if test mode is enabled
func (c *Client) IsTestMode() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.testMode
}

// Public methods (MarketDataBroker interface)

// Ping tests connectivity to the Binance API
func (c *Client) Ping(ctx context.Context) error {
	return c.client.NewPingService().Do(ctx)
}

// GetServerTime gets the current server time
func (c *Client) GetServerTime(ctx context.Context) (int64, error) {
	serverTime, err := c.client.NewSetServerTimeService().Do(ctx)
	if err != nil {
		return 0, err
	}
	return serverTime, nil
}

// GetExchangeInfo gets current exchange trading rules and symbol information
func (c *Client) GetExchangeInfo(ctx context.Context) (*binance.ExchangeInfo, error) {
	return c.client.NewExchangeInfoService().Do(ctx)
}

// GetTickerPrice gets the latest price for a symbol
func (c *Client) GetTickerPrice(ctx context.Context, symbol string) (string, error) {
	ticker, err := c.client.NewListPricesService().Symbol(symbol).Do(ctx)
	if err != nil {
		return "", err
	}
	if len(ticker) == 0 {
		return "", fmt.Errorf("no price data for symbol %s", symbol)
	}
	return ticker[0].Price, nil
}

// GetKlines gets kline/candlestick data for a symbol
func (c *Client) GetKlines(ctx context.Context, symbol, interval string, limit int) ([]*binance.Kline, error) {
	klines, err := c.client.NewKlinesService().
		Symbol(symbol).
		Interval(interval).
		Limit(limit).
		Do(ctx)
	if err != nil {
		return nil, err
	}
	return klines, nil
}

// GetOrderBook gets the current order book for a symbol
func (c *Client) GetOrderBook(ctx context.Context, symbol string, limit int) (*OrderBook, error) {
	// Get order book from Binance API
	binanceBook, err := c.client.NewDepthService().Symbol(symbol).Limit(limit).Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get order book: %w", err)
	}

	// Convert Binance order book to our format
	book := &OrderBook{
		LastUpdateID: binanceBook.LastUpdateID,
		Symbol:       symbol,
		Timestamp:    time.Now().UnixNano() / int64(time.Millisecond),
		Bids:         make([]OrderBookEntry, 0, len(binanceBook.Bids)),
		Asks:         make([]OrderBookEntry, 0, len(binanceBook.Asks)),
	}

	// Process bids (already sorted from highest to lowest)
	for _, bid := range binanceBook.Bids {
		book.Bids = append(book.Bids, OrderBookEntry{
			Price:    bid.Price,
			Quantity: bid.Quantity,
		})
	}

	// Process asks (already sorted from lowest to highest)
	for _, ask := range binanceBook.Asks {
		book.Asks = append(book.Asks, OrderBookEntry{
			Price:    ask.Price,
			Quantity: ask.Quantity,
		})
	}

	return book, nil
}

// SubscribeToOrderBook subscribes to order book updates for a symbol
func (c *Client) SubscribeToOrderBook(ctx context.Context, symbol string, handler func(book *OrderBook)) (doneC, stopC chan struct{}, err error) {
	doneC = make(chan struct{})
	stopC = make(chan struct{})

	// Create a new websocket client
	wsDepthHandler := func(event *binance.WsDepthEvent) {
		book := &OrderBook{
			LastUpdateID: event.LastUpdateID,
			Symbol:       event.Symbol,
			Timestamp:    time.Now().UnixNano() / int64(time.Millisecond),
			Bids:         make([]OrderBookEntry, 0, len(event.Bids)),
			Asks:         make([]OrderBookEntry, 0, len(event.Asks)),
		}

		// Process bids
		for _, bid := range event.Bids {
			book.Bids = append(book.Bids, OrderBookEntry{
				Price:    bid.Price,
				Quantity: bid.Quantity,
			})
		}

		// Process asks
		for _, ask := range event.Asks {
			book.Asks = append(book.Asks, OrderBookEntry{
				Price:    ask.Price,
				Quantity: ask.Quantity,
			})
		}

		handler(book)
	}

	errHandler := func(err error) {
		log.Printf("Websocket error: %v", err)
	}

	// Start WebSocket connection
	_, _, err = binance.WsDepthServe(symbol, wsDepthHandler, errHandler)
	if err != nil {
		close(doneC)
		close(stopC)
		return nil, nil, fmt.Errorf("failed to start websocket: %w", err)
	}

	// Handle graceful shutdown
	go func() {
		select {
		case <-ctx.Done():
			// Context was canceled
		case <-stopC:
			// Stop was requested
		}
		// The actual WebSocket connection is managed by the binance package,
		// we just need to notify the caller that we're done
		close(doneC)
	}()

	return doneC, stopC, nil
}

// Private methods (TradingBroker interface)

// GetAccountInfo gets the current account information
func (c *Client) GetAccountInfo(ctx context.Context) (*binance.Account, error) {
	if c.IsTestMode() {
		return nil, fmt.Errorf("test mode is enabled - use test broker for testing")
	}

	res, err := c.client.NewGetAccountService().Do(ctx)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GetBalance gets the balance for a specific asset
func (c *Client) GetBalance(ctx context.Context, asset string) (*binance.Balance, error) {
	if c.IsTestMode() {
		return nil, fmt.Errorf("test mode is enabled - use test broker for testing")
	}

	account, err := c.GetAccountInfo(ctx)
	if err != nil {
		return nil, err
	}

	for _, balance := range account.Balances {
		if balance.Asset == asset {
			return &balance, nil
		}
	}

	return nil, nil
}

// CreateOrder creates a new order
func (c *Client) CreateOrder(ctx context.Context, order *OrderRequest) (*OrderResponse, error) {
	if c.IsTestMode() {
		return nil, fmt.Errorf("test mode is enabled - use test broker for testing")
	}

	service := c.client.NewCreateOrderService()
	service.Symbol(order.Symbol)
	service.Side(binance.SideType(order.Side))
	service.Type(binance.OrderType(order.Type))
	service.Quantity(order.Quantity)

	if order.TimeInForce != "" {
		service.TimeInForce(binance.TimeInForceType(order.TimeInForce))
	}

	if order.Price != "" {
		service.Price(order.Price)
	}

	if order.NewClientOrderID != "" {
		service.NewClientOrderID(order.NewClientOrderID)
	}

	res, err := service.Do(ctx)
	if err != nil {
		return nil, err
	}

	return &OrderResponse{
		OrderID:       res.OrderID,
		ClientOrderID: res.ClientOrderID,
		Status:        string(res.Status),
	}, nil
}

// GetOpenOrders gets all open orders for a symbol
func (c *Client) GetOpenOrders(ctx context.Context, symbol string) ([]*binance.Order, error) {
	if c.IsTestMode() {
		return nil, fmt.Errorf("test mode is enabled - use test broker for testing")
	}

	return c.client.NewListOpenOrdersService().Symbol(symbol).Do(ctx)
}

// CancelOrder cancels an order
func (c *Client) CancelOrder(ctx context.Context, symbol string, orderID int64) error {
	if c.IsTestMode() {
		return fmt.Errorf("test mode is enabled - use test broker for testing")
	}

	_, err := c.client.NewCancelOrderService().
		Symbol(symbol).
		OrderID(orderID).
		Do(ctx)
	return err
}

// SubscribeToMarketData subscribes to market data updates
func (c *Client) SubscribeToMarketData(ctx context.Context, symbol, interval string, handler WsKlineHandler) (doneC, stopC chan struct{}, err error) {
	// Create channels
	doneC = make(chan struct{})
	stopC = make(chan struct{})

	// Start a goroutine to handle the subscription
	go func() {
		// Convert our handler to the Binance SDK's handler type
		binanceHandler := func(event *binance.WsKlineEvent) {
			handler(&WsKline{
				StartTime:  event.Kline.StartTime,
				CloseTime:  event.Kline.EndTime,  // Changed from CloseTime to EndTime
				Symbol:     event.Symbol,
				Interval:   event.Kline.Interval,
				FirstTrade: event.Kline.FirstTradeID,
				LastTrade:  event.Kline.LastTradeID,
				Open:       event.Kline.Open,
				Close:      event.Kline.Close,
				High:       event.Kline.High,
				Low:        event.Kline.Low,
				Volume:     event.Kline.Volume,
				IsFinal:    event.Kline.IsFinal,
			})
		}

		// Start the websocket connection
		doneWsC, stopWsC, err := binance.WsKlineServe(symbol, interval, binanceHandler, func(err error) {
			log.Printf("Websocket error: %v", err)
			close(doneC)
		})


		if err != nil {
			log.Printf("Failed to subscribe to market data: %v", err)
			close(doneC)
			return
		}

		// Handle stop signal or context cancellation
		go func() {
			select {
			case <-stopC:
				// Stop requested
			case <-ctx.Done():
				// Context cancelled
			}

			// Close the websocket connection
			close(stopWsC)

			// Close the done channel to signal completion
			close(doneC)
		}()

		// Wait for the websocket to be done
		<-doneWsC
	}()

	return doneC, stopC, nil
}
