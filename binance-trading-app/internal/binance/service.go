package binance

import (
	"context"
	"fmt"
	"log"
	"sync"

	"github.com/adshao/go-binance/v2"
	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// Service handles Binance API operations
type Service struct {
	client    *binance.Client
	isTestnet bool
	ctx       context.Context
	mu        sync.RWMutex
}

// NewService creates a new Binance service
func NewService() *Service {
	return &Service{
		client: binance.NewClient("", ""),
	}
}

// SetContext sets the context for the service
func (s *Service) SetContext(ctx context.Context) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.ctx = ctx
}

// SetAPIKey sets the API key and secret for the Binance client
func (s *Service) SetAPIKey(apiKey, apiSecret string, isTestnet bool) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if apiKey == "" || apiSecret == "" {
		return fmt.Errorf("API key and secret cannot be empty")
	}

	s.client = binance.NewClient(apiKey, apiSecret)
	s.isTestnet = isTestnet

	if isTestnet {
		binance.UseTestnet = true
	} else {
		binance.UseTestnet = false
	}

	// Test the connection
	_, err := s.GetServerTime()
	if err != nil {
		return fmt.Errorf("failed to connect to Binance API: %w", err)
	}

	log.Printf("Successfully connected to Binance %s", map[bool]string{true: "Testnet", false: "Live"}[isTestnet])
	return nil
}

// IsConnected checks if the service is properly connected to the Binance API
func (s *Service) IsConnected() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// If client is not initialized
	if s.client == nil {
		return false
	}

	// Try to get server time as a connectivity test
	_, err := s.client.NewServerTimeService().Do(s.ctx)
	return err == nil
}

// Ping tests connectivity to the Binance API
func (s *Service) Ping() error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.client.NewPingService().Do(s.ctx)
}

// GetServerTime gets the current server time
func (s *Service) GetServerTime() (int64, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	serverTime, err := s.client.NewServerTimeService().Do(s.ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to get server time: %w", err)
	}
	return serverTime, nil
}

// GetAccount gets the Binance account information
func (s *Service) GetAccount() (*binance.Account, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.client == nil {
		return nil, fmt.Errorf("not connected to Binance API")
	}

	account, err := s.client.NewGetAccountService().Do(s.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get account info: %w", err)
	}
	return account, nil
}

// GetBalances gets the account balances
func (s *Service) GetBalances() ([]binance.Balance, error) {
	account, err := s.GetAccount()
	if err != nil {
		return nil, err
	}

	// Filter out zero balances
	var balances []binance.Balance
	for _, balance := range account.Balances {
		if balance.Free != "0" || balance.Locked != "0" {
			balances = append(balances, balance)
		}
	}

	return balances, nil
}

// GetBalance gets the balance for a specific asset
func (s *Service) GetBalance(asset string) (map[string]string, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.client == nil {
		return nil, fmt.Errorf("not connected to Binance API")
	}

	balances, err := s.GetBalances()
	if err != nil {
		return nil, fmt.Errorf("failed to get balances: %w", err)
	}

	// Look for the specific asset (case-insensitive)
	for _, balance := range balances {
		if balance.Asset == asset {
			return map[string]string{
				"free":   balance.Free,
				"locked": balance.Locked,
			}, nil
		}
	}

	return nil, fmt.Errorf("asset %s not found or has zero balance", asset)
}

// GetTickerPrice gets the current price for a symbol
func (s *Service) GetTickerPrice(symbol string) (string, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.client == nil {
		return "", fmt.Errorf("not connected to Binance API")
	}

	prices, err := s.client.NewListPricesService().Symbol(symbol).Do(s.ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get price for %s: %w", symbol, err)
	}

	if len(prices) == 0 {
		return "", fmt.Errorf("no price data for symbol: %s", symbol)
	}

	return prices[0].Price, nil
}

// SubscribeToMarketData subscribes to market data updates
func (s *Service) SubscribeToMarketData(symbol, interval string, handler binance.WsKlineHandler) (doneC, stopC chan struct{}, err error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.client == nil {
		return nil, nil, fmt.Errorf("not connected to Binance API")
	}

	errHandler := func(err error) {
		log.Printf("WebSocket error: %v", err)
	}

	return binance.WsKlineServe(symbol, interval, handler, errHandler)
}

// BinanceService provides methods for the frontend to interact with Binance
type BinanceService struct {
	ctx    context.Context
	client *Service
}

// NewBinanceService creates a new BinanceService
func NewBinanceService() *BinanceService {
	return &BinanceService{
		client: NewService(),
	}
}

// SetContext sets the Wails context
func (s *BinanceService) SetContext(ctx context.Context) {
	s.ctx = ctx
	s.client.SetContext(ctx)
}

// SetAPIKeys updates the API keys for the Binance client
func (s *BinanceService) SetAPIKeys(apiKey, apiSecret string, isTestnet bool) error {
	return s.client.SetAPIKey(apiKey, apiSecret, isTestnet)
}

// GetAccountInfo retrieves account information from Binance
func (s *BinanceService) GetAccountInfo() (map[string]interface{}, error) {
	account, err := s.client.GetAccount()
	if err != nil {
		runtime.LogError(s.ctx, "Failed to get account info: "+err.Error())
		return nil, err
	}

	// Convert to a simple map for the frontend
	result := make(map[string]interface{})
	result["makerCommission"] = account.MakerCommission
	result["takerCommission"] = account.TakerCommission
	result["buyerCommission"] = account.BuyerCommission
	result["sellerCommission"] = account.SellerCommission
	result["canTrade"] = account.CanTrade
	result["canWithdraw"] = account.CanWithdraw
	result["canDeposit"] = account.CanDeposit
	
	// Add balances
	balances, err := s.client.GetBalances()
	if err != nil {
		return nil, err
	}
	balancesMap := make([]map[string]string, 0, len(balances))
	for _, b := range balances {
		balancesMap = append(balancesMap, map[string]string{
			"asset":  b.Asset,
			"free":   b.Free,
			"locked": b.Locked,
		})
	}
	result["balances"] = balancesMap

	return result, nil
}

// GetBalance gets the balance for a specific asset
func (s *BinanceService) GetBalance(asset string) (map[string]string, error) {
	balances, err := s.client.GetBalances()
	if err != nil {
		return nil, err
	}
	for _, b := range balances {
		if b.Asset == asset {
			return map[string]string{
				"asset":  b.Asset,
				"free":   b.Free,
				"locked": b.Locked,
			}, nil
		}
		return map[string]string{
			"asset":  asset,
			"free":   "0",
			"locked": "0",
		}, nil
	}

	return map[string]string{
		"asset":  asset,
		"free":   "0",
		"locked": "0",
	}, nil
}
