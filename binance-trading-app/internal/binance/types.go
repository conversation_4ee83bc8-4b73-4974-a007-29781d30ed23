package binance

// OrderRequest represents a request to create a new order
type OrderRequest struct {
	Symbol           string          `json:"symbol"`
	Side            OrderSide       `json:"side"`
	Type            OrderType       `json:"type"`
	TimeInForce     TimeInForceType `json:"timeInForce,omitempty"`
	Quantity        string          `json:"quantity"`
	Price           string          `json:"price,omitempty"`
	NewClientOrderID string         `json:"newClientOrderId,omitempty"`
	StopPrice       string          `json:"stopPrice,omitempty"`
	IcebergQuantity string          `json:"icebergQty,omitempty"`
}

// OrderResponse represents the response from creating a new order
type OrderResponse struct {
	Symbol          string          `json:"symbol"`
	OrderID         int64           `json:"orderId"`
	ClientOrderID   string          `json:"clientOrderId"`
	TransactTime    int64           `json:"transactTime"`
	Price           string          `json:"price"`
	OrigQty         string          `json:"origQty"`
	ExecutedQty     string          `json:"executedQty"`
	Status          string          `json:"status"`
	TimeInForce     string          `json:"timeInForce"`
	Type            string          `json:"type"`
	Side            string          `json:"side"`
	Fills           []OrderFill     `json:"fills,omitempty"`
}

// OrderFill represents a fill of an order
type OrderFill struct {
	Price           string `json:"price"`
	Qty             string `json:"qty"`
	Commission      string `json:"commission"`
	CommissionAsset string `json:"commissionAsset"`
}

// OrderBookEntry represents a single entry in the order book
type OrderBookEntry struct {
	Price    string `json:"price"`
	Quantity string `json:"quantity"`
}

// OrderBook represents the current state of the order book
// Bids and Asks are sorted by price, with best prices first
// Bids are sorted in descending order (highest bid first)
// Asks are sorted in ascending order (lowest ask first)
type OrderBook struct {
	LastUpdateID int64           `json:"lastUpdateId"`
	Bids         []OrderBookEntry `json:"bids"`
	Asks         []OrderBookEntry `json:"asks"`
	Symbol       string          `json:"symbol"`
	Timestamp    int64           `json:"timestamp"`
}

// OrderSide represents the side of an order (buy/sell)
type OrderSide string

// OrderType represents the type of an order (limit, market, etc.)
type OrderType string

// TimeInForceType represents the time in force for an order
type TimeInForceType string

// OrderStatus represents the status of an order
const (
	// Order sides
	OrderSideBuy  OrderSide = "BUY"
	OrderSideSell OrderSide = "SELL"

	// Order types
	OrderTypeLimit           OrderType = "LIMIT"
	OrderTypeMarket          OrderType = "MARKET"
	OrderTypeStopLoss        OrderType = "STOP_LOSS"
	OrderTypeStopLossLimit   OrderType = "STOP_LOSS_LIMIT"
	OrderTypeTakeProfit      OrderType = "TAKE_PROFIT"
	OrderTypeTakeProfitLimit OrderType = "TAKE_PROFIT_LIMIT"

	// Time in force
	TimeInForceGTC TimeInForceType = "GTC" // Good Till Cancel
	TimeInForceIOC TimeInForceType = "IOC" // Immediate or Cancel
	TimeInForceFOK TimeInForceType = "FOK" // Fill or Kill
)
