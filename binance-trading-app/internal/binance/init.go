package binance

import "log"

// InitTradingService initializes a new trading service with the given configuration
func InitTradingService(cfg *Config) (*TradingService, error) {
	if cfg == nil {
		// Default configuration
		cfg = &Config{
			TestMode: true, // Default to test mode for safety
		}
	}

	// Create the appropriate broker
	var broker Broker
	if cfg.TestMode {
		log.Println("Initializing test broker")
		// Use the test broker from the test package
		broker = nil // TODO: Implement test broker integration
	} else {
		log.Println("Initializing real Binance broker")
		client := NewClient(cfg)
		broker = client
	}

	// Create and return the trading service
	return NewTradingService(broker), nil
}

// InitMarketDataService initializes a new market data service
func InitMarketDataService() (*TradingService, error) {
	// Always use real market data
	cfg := &Config{TestMode: false}
	client := NewClient(cfg)
	return NewTradingService(client), nil
}
