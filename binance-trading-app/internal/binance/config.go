package binance

// Config holds the configuration for the Binance client
type Config struct {
	APIKey    string `json:"api_key"`
	APISecret string `json:"api_secret"`
	TestNet   bool   `json:"test_net"`
	TestMode  bool   `json:"test_mode"` // When true, uses test broker for private methods
}

// NewConfig creates a new Config instance with default values
func NewConfig() *Config {
	return &Config{
		TestNet: true, // Используем тестовую среду по умолчанию
	}
}
