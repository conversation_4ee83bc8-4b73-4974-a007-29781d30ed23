package binance

import (
	"context"

	"github.com/adshao/go-binance/v2"
)

// <PERSON><PERSON><PERSON> defines the interface for interacting with the exchange
// It separates public (market data) and private (account/trading) operations
type Broker interface {
	MarketDataBroker
	TradingBroker

	// Test mode control
	SetTestMode(enabled bool)
	IsTestMode() bool
}

// WsKline represents a websocket kline/candlestick event
type WsKline struct {
	StartTime  int64  `json:"t"`
	CloseTime  int64  `json:"T"`
	Symbol     string `json:"s"`
	Interval   string `json:"i"`
	FirstTrade int64  `json:"f"`
	LastTrade  int64  `json:"L"`
	Open       string `json:"o"`
	Close      string `json:"c"`
	High       string `json:"h"`
	Low        string `json:"l"`
	Volume     string `json:"v"`
	IsFinal    bool   `json:"x"`
}

// WsKlineHandler handles websocket kline events
type WsKlineHandler func(event *WsKline)

// MarketDataBroker defines interface for market data operations
type MarketDataBroker interface {
	// Public methods (market data)
	Ping(ctx context.Context) error
	GetServerTime(ctx context.Context) (int64, error)
	GetExchangeInfo(ctx context.Context) (*binance.ExchangeInfo, error)
	GetTickerPrice(ctx context.Context, symbol string) (string, error)
	GetKlines(ctx context.Context, symbol, interval string, limit int) ([]*binance.Kline, error)
	
	// Order book methods
	GetOrderBook(ctx context.Context, symbol string, limit int) (*OrderBook, error)
	SubscribeToOrderBook(ctx context.Context, symbol string, handler func(book *OrderBook)) (doneC, stopC chan struct{}, err error)
	
	// Market data subscription
	SubscribeToMarketData(ctx context.Context, symbol, interval string, handler WsKlineHandler) (doneC, stopC chan struct{}, err error)
}

// TradingBroker defines interface for trading operations
type TradingBroker interface {
	// Private methods (account/trading)
	GetAccountInfo(ctx context.Context) (*binance.Account, error)
	GetBalance(ctx context.Context, asset string) (*binance.Balance, error)
	CreateOrder(ctx context.Context, order *OrderRequest) (*OrderResponse, error)
	GetOpenOrders(ctx context.Context, symbol string) ([]*binance.Order, error)
	CancelOrder(ctx context.Context, symbol string, orderID int64) error
}
