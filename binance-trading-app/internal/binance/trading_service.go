package binance

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// TradingService handles trading operations using the Broker interface
type TradingService struct {
	broker    Broker
	ctx       context.Context
	mu        sync.RWMutex
	apiKey    string
	apiSecret string
	testMode  bool
}

// NewTradingService creates a new trading service with the given broker
func NewTradingService(broker Broker) *TradingService {
	return &TradingService{
		broker: broker,
	}
}

// SetContext sets the Wails context for the service
func (s *TradingService) SetContext(ctx context.Context) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.ctx = ctx
}

// SetAPIKey sets the API key and secret for the service
func (s *TradingService) SetAPIKey(apiKey, apiSecret string, testMode bool) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.apiKey = apiKey
	s.apiSecret = apiSecret
	s.testMode = testMode

	// Reinitialize the broker with the new credentials
	s.broker = NewBroker(&Config{
		APIKey:    apiKey,
		APISecret: apiSecret,
		TestMode:  testMode,
	})

	return nil
}

// SetTestMode enables or disables test mode
func (s *TradingService) SetTestMode(enabled bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.broker.SetTestMode(enabled)
}

// IsTestMode returns true if test mode is enabled
func (s *TradingService) IsTestMode() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.broker.IsTestMode()
}

// GetAccountInfo gets the account information
func (s *TradingService) GetAccountInfo() (map[string]interface{}, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	account, err := s.broker.GetAccountInfo(s.ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get account info: %w", err)
	}

	// Convert to map for frontend
	result := map[string]interface{}{
		"makerCommission":  account.MakerCommission,
		"takerCommission":  account.TakerCommission,
		"buyerCommission":  account.BuyerCommission,
		"sellerCommission": account.SellerCommission,
		"canTrade":         account.CanTrade,
		"canWithdraw":      account.CanWithdraw,
		"canDeposit":       account.CanDeposit,
	}

	// Add balances
	balances := make(map[string]map[string]string)
	for _, b := range account.Balances {
		if b.Free != "0" || b.Locked != "0" {
			balances[b.Asset] = map[string]string{
				"free":   b.Free,
				"locked": b.Locked,
			}
		}
	}
	result["balances"] = balances

	return result, nil
}

// GetBalance gets the balance for a specific asset
func (s *TradingService) GetBalance(asset string) (map[string]string, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	balance, err := s.broker.GetBalance(s.ctx, asset)
	if err != nil {
		return nil, fmt.Errorf("failed to get balance for %s: %w", asset, err)
	}

	return map[string]string{
		"asset":  asset,
		"free":   balance.Free,
		"locked": balance.Locked,
	}, nil
}

// CreateOrder creates a new order
func (s *TradingService) CreateOrder(symbol, side, orderType, quantity, price, timeInForce, clientOrderID string) (*OrderResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Convert string parameters to their respective types
	sideType := OrderSide(side)
	typeType := OrderType(orderType)
	
	// Only set TimeInForce for order types that require it
	var tifType TimeInForceType
	if typeType == OrderTypeLimit || typeType == OrderTypeStopLossLimit || typeType == OrderTypeTakeProfitLimit {
		tifType = TimeInForceType(timeInForce)
	} else {
		tifType = "" // Empty for market orders
	}

	order := &OrderRequest{
		Symbol:           symbol,
		Side:            sideType,
		Type:            typeType,
		Quantity:        quantity,
		Price:           price,
		TimeInForce:     tifType,
		NewClientOrderID: clientOrderID,
	}

	return s.broker.CreateOrder(s.ctx, order)
}

// GetOpenOrders gets all open orders for a symbol
func (s *TradingService) GetOpenOrders(symbol string) ([]map[string]interface{}, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	orders, err := s.broker.GetOpenOrders(s.ctx, symbol)
	if err != nil {
		return nil, fmt.Errorf("failed to get open orders: %w", err)
	}

	// Convert to slice of maps for frontend
	result := make([]map[string]interface{}, 0, len(orders))
	for _, o := range orders {
		result = append(result, map[string]interface{}{
			"orderId":       o.OrderID,
			"symbol":        o.Symbol,
			"side":          o.Side,
			"type":          o.Type,
			"price":         o.Price,
			"origQty":       o.OrigQuantity,
			"executedQty":   o.ExecutedQuantity,
			"status":        o.Status,
			"timeInForce":   o.TimeInForce,
			"time":          o.Time,
			"clientOrderId": o.ClientOrderID,
		})
	}

	return result, nil
}

// CancelOrder cancels an order
func (s *TradingService) CancelOrder(symbol string, orderID int64) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.broker.CancelOrder(s.ctx, symbol, orderID)
}

// SubscribeToMarketData subscribes to market data updates
func (s *TradingService) SubscribeToMarketData(symbol, interval string, callback func(data []byte)) (func(), error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Check if we have a market data broker
	marketBroker, ok := s.broker.(MarketDataBroker)
	if !ok {
		return nil, fmt.Errorf("broker does not support market data operations")
	}

	doneC, stopC, err := marketBroker.SubscribeToMarketData(s.ctx, symbol, interval, func(event *WsKline) {
		// Convert to frontend-friendly format
		data := map[string]interface{}{
			"eventType":  "kline",
			"symbol":     symbol,
			"interval":   interval,
			"openTime":   event.StartTime,
			"closeTime":  event.CloseTime,
			"openPrice":  event.Open,
			"closePrice": event.Close,
			"highPrice":  event.High,
			"lowPrice":   event.Low,
			"volume":     event.Volume,
			"isFinal":    event.IsFinal,
		}

		jsonData, err := json.Marshal(data)
		if err != nil {
			log.Printf("Failed to marshal market data: %v", err)
			return
		}

		if callback != nil {
			callback(jsonData)
		} else if s.ctx != nil {
			// If no callback provided, emit to frontend using Wails
			runtime.EventsEmit(s.ctx, "marketDataUpdate", string(jsonData))
		}
	})

	if err != nil {
		return nil, fmt.Errorf("failed to subscribe to market data: %w", err)
	}

	// Return a function to stop the subscription
	return func() {
		if stopC != nil {
			stopC <- struct{}{}
		}
		if doneC != nil {
			close(doneC)
		}
	}, nil
}
