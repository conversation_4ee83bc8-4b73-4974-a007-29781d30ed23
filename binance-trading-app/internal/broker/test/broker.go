package test

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"sync"
	"time"

	"github.com/google/uuid"
	"binance-trading-app/internal/broker/types"
)

// Ensure TestBroker implements types.Broker
var _ types.Broker = (*TestBroker)(nil)

// TestBroker реализует интерфейс types.Broker для тестового режима
type TestBroker struct {
	mu           sync.RWMutex
	testMode    bool
	initialized  bool

	// Балансы
	balances map[types.Asset]types.Balance

	// Активные ордера
	orders map[string]*types.Order

	// Подписки на свечи
	candleSubs map[string]map[chan []types.Candle]struct{}

	// Подписки на стакан
	orderbookSubs map[string]map[chan types.OrderBook]struct{}

	// История сделок
	trades []*types.Order

	// Контекст для управления горутинами
	ctx        context.Context
	cancelFunc context.CancelFunc
	wg         sync.WaitGroup
}

// NewTestBroker создает новый экземпляр тестового брокера
func NewTestBroker() *TestBroker {
	ctx, cancel := context.WithCancel(context.Background())
	b := &TestBroker{
		testMode:      true,
		balances:     make(map[types.Asset]types.Balance),
		orders:       make(map[string]*types.Order),
		candleSubs:   make(map[string]map[chan []types.Candle]struct{}),
		orderbookSubs: make(map[string]map[chan types.OrderBook]struct{}),
		trades:         make([]*types.Order, 0),
		ctx:            ctx,
		cancelFunc:     cancel,
	}

	// Initialize with some test balances
	b.balances[types.Asset("USDT")] = types.Balance{Asset: "USDT", Free: 10000, Locked: 0}
	b.balances[types.Asset("BTC")] = types.Balance{Asset: "BTC", Free: 1, Locked: 0}
	b.balances[types.Asset("ETH")] = types.Balance{Asset: "ETH", Free: 10, Locked: 0}

	return b
}

// SetAPIKey устанавливает API ключи для брокера
func (b *TestBroker) SetAPIKey(apiKey, apiSecret string, isTestnet bool) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	// В тестовом режиме просто логируем установку ключей
	log.Printf("Test broker: API keys set (testnet: %v), key: %s...%s, secret: %s...%s",
		isTestnet,
		safeSubstring(apiKey, 0, 3),
		safeSubstring(apiKey, len(apiKey)-3, 3),
		safeSubstring(apiSecret, 0, 3),
		safeSubstring(apiSecret, len(apiSecret)-3, 3),
	)

	// Переключаем режим тестовой сети, если нужно
	b.testMode = isTestnet

	return nil
}

// safeSubstring возвращает подстроку с проверкой границ
func safeSubstring(s string, start, length int) string {
	runes := []rune(s)
	if start < 0 {
		start = 0
	}
	if start > len(runes) {
		return ""
	}
	end := start + length
	if end > len(runes) {
		end = len(runes)
	}
	return string(runes[start:end])
}

// Connect имитирует подключение к бирже
func (b *TestBroker) Connect(ctx context.Context) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if b.initialized {
		return nil
	}

	// Инициализируем тестовый баланс
	b.balances = map[types.Asset]types.Balance{
		"USDT": {Asset: "USDT", Free: 10000, Locked: 0},
		"BTC":  {Asset: "BTC", Free: 1, Locked: 0},
		"ETH":  {Asset: "ETH", Free: 10, Locked: 0},
	}

	b.initialized = true
	return nil
}

// IsConnected возвращает статус подключения
func (b *TestBroker) IsConnected() bool {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.initialized
}

// GetExchangeInfo возвращает тестовую информацию о торговых парах
func (b *TestBroker) GetExchangeInfo(ctx context.Context) ([]types.Symbol, error) {
	return []types.Symbol{
		{
			ID:          "BTCUSDT",
			BaseAsset:   "BTC",
			QuoteAsset:  "USDT",
			MinPrice:    0.01,
			MaxPrice:    1000000,
			TickSize:    0.01,
			MinQty:      0.00001,
			MaxQty:      1000,
			StepSize:    0.00001,
			MinNotional: 10,
		},
		{
			ID:          "ETHUSDT",
			BaseAsset:   "ETH",
			QuoteAsset:  "USDT",
			MinPrice:    0.01,
			MaxPrice:    100000,
			TickSize:    0.01,
			MinQty:      0.0001,
			MaxQty:      10000,
			StepSize:    0.0001,
			MinNotional: 10,
		},
	}, nil
}

// GetAccountInfo возвращает тестовый баланс
func (b *TestBroker) GetAccountInfo(ctx context.Context) ([]types.Balance, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	balances := make([]types.Balance, 0, len(b.balances))
	for _, balance := range b.balances {
		balances = append(balances, balance)
	}

	return balances, nil
}

// GetBalance возвращает баланс по активу
func (b *TestBroker) GetBalance(ctx context.Context, asset string) (*types.Balance, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	assetType := types.Asset(asset)
	balance, exists := b.balances[assetType]
	if !exists {
		return nil, fmt.Errorf("asset %s not found", asset)
	}

	return &balance, nil
}

// CreateOrder создает тестовый ордер
func (b *TestBroker) CreateOrder(ctx context.Context, symbol, side, orderType string, quantity, price float64) (*types.Order, error) {
	b.mu.Lock()
	defer b.mu.Unlock()

	// Валидация параметров
	if quantity <= 0 {
		return nil, fmt.Errorf("invalid quantity")
	}

	if orderType != "MARKET" && price <= 0 {
		return nil, fmt.Errorf("price must be greater than 0 for %s orders", orderType)
	}

	// Создаем новый ордер
	now := time.Now().UnixMilli()
	order := &types.Order{
		ID:           uuid.New().String(),
		Symbol:       symbol,
		Side:         side,
		Type:         orderType,
		Price:        price,
		Quantity:     quantity,
		ExecutedQty:  0,
		Status:       "NEW",
		TimeInForce:  "GTC",
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	// Добавляем ордер в список активных
	b.orders[order.ID] = order

	// В тестовом режиме сразу исполняем рыночные ордера
	if orderType == "MARKET" {
		return b.executeOrder(order)
	}

	return order, nil
}

// executeOrder выполняет ордер
func (b *TestBroker) executeOrder(order *types.Order) (*types.Order, error) {
	// Обновляем статус ордера
	order.Status = "FILLED"
	order.ExecutedQty = order.Quantity
	order.UpdatedAt = time.Now().UnixMilli()

	// Обновляем балансы
	baseAsset := order.Symbol[:3]  // Упрощенное извлечение базового актива
	quoteAsset := order.Symbol[3:] // Упрощенное извлечение котируемого актива

	if order.Side == "BUY" {
		// Покупка: уменьшаем котируемую валюту, увеличиваем базовую
		b.updateBalance(quoteAsset, -order.Price*order.Quantity)
		b.updateBalance(baseAsset, order.Quantity)
	} else {
		// Продажа: уменьшаем базовую валюту, увеличиваем котируемую
		b.updateBalance(baseAsset, -order.Quantity)
		b.updateBalance(quoteAsset, order.Price*order.Quantity)
	}

	// Добавляем в историю сделок
	b.trades = append(b.trades, order)

	// Удаляем из активных ордеров
	delete(b.orders, order.ID)

	return order, nil
}

// updateBalance обновляет баланс актива
func (b *TestBroker) updateBalance(asset string, amount float64) {
	assetKey := types.Asset(asset)
	balance := b.balances[assetKey]
	if balance.Asset == "" {
		balance.Asset = asset
	}
	balance.Free += amount
	b.balances[assetKey] = balance
}

// GetOrder возвращает информацию об ордере
func (b *TestBroker) GetOrder(ctx context.Context, orderID, symbol string) (*types.Order, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	order, exists := b.orders[orderID]
	if !exists {
		return nil, fmt.Errorf("order %s not found", orderID)
	}

	// Создаем копию, чтобы избежать гонок данных
	orderCopy := *order
	return &orderCopy, nil
}

// CancelOrder отменяет ордер
func (b *TestBroker) CancelOrder(ctx context.Context, orderID, symbol string) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	order, exists := b.orders[orderID]
	if !exists {
		return fmt.Errorf("order %s not found", orderID)
	}

	order.Status = "CANCELED"
	order.UpdatedAt = time.Now().UnixMilli()

	// Удаляем из активных ордеров
	delete(b.orders, orderID)

	return nil
}

// GetOpenOrders возвращает список активных ордеров
func (b *TestBroker) GetOpenOrders(ctx context.Context, symbol string) ([]*types.Order, error) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	var result []*types.Order
	for _, order := range b.orders {
		if symbol == "" || order.Symbol == symbol {
			orderCopy := *order
			result = append(result, &orderCopy)
		}
	}

	return result, nil
}

// SubscribeToCandles подписывается на обновления свечей
func (b *TestBroker) SubscribeToCandles(symbol, interval string) (chan []types.Candle, error) {
	b.mu.Lock()
	defer b.mu.Unlock()

	ch := make(chan []types.Candle, 100)

	if _, exists := b.candleSubs[symbol]; !exists {
		b.candleSubs[symbol] = make(map[chan []types.Candle]struct{})
	}

	b.candleSubs[symbol][ch] = struct{}{}

	// Запускаем генератор тестовых данных, если это первая подписка
	if len(b.candleSubs[symbol]) == 1 {
		b.wg.Add(1)
		go b.generateTestCandles(symbol, interval, ch)
	}

	return ch, nil
}

// UnsubscribeFromCandles отписывается от обновлений свечей
func (b *TestBroker) UnsubscribeFromCandles(symbol string, ch chan []types.Candle) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if subs, exists := b.candleSubs[symbol]; exists {
		if _, ok := subs[ch]; ok {
			delete(subs, ch)
			close(ch)
			if len(subs) == 0 {
				delete(b.candleSubs, symbol)
			}
			return nil
		}
	}
	return fmt.Errorf("subscription not found")
}

// generateTestCandles генерирует тестовые свечи
func (b *TestBroker) generateTestCandles(symbol, interval string, ch chan<- []types.Candle) {
	defer b.wg.Done()

	ticker := time.NewTicker(time.Second) // Обновляем раз в секунду для тестов
	defer ticker.Stop()

	// Начальная цена
	price := 50000.0

	for {
		select {
		case <-b.ctx.Done():
			close(ch)
			return
		case <-ticker.C:
			// Генерируем случайное изменение цены
			change := (rand.Float64() - 0.5) * 0.01 // ±0.5%
			price = price * (1 + change)

			now := time.Now()
			candle := types.Candle{
				OpenTime:  now.Add(-time.Minute).Unix() * 1000, // 1 минута назад
				CloseTime: now.Unix() * 1000,
				Open:      price * 0.999,
				High:      price * 1.001,
				Low:       price * 0.998,
				Close:     price,
				Volume:    10 + rand.Float64()*90, // Объем 10-100
			}

			// Отправляем свечу
			select {
			case ch <- []types.Candle{candle}:
			default:
				log.Println("candle channel full, dropping update")
			}

			// Проверяем исполнение ордеров
			b.checkOrdersForExecution(candle)
		}
	}
}

// checkOrdersForExecution проверяет и исполняет ордера на основе ценового диапазона свечи
func (b *TestBroker) checkOrdersForExecution(candle types.Candle) {
	b.mu.Lock()
	defer b.mu.Unlock()

	now := time.Now().UnixMilli()
	for _, order := range b.orders {
		// Пропускаем исполненные и отмененные ордера
		if order.Status == "FILLED" || order.Status == "CANCELED" {
			continue
		}

		// Обновляем время последней проверки
		order.LastCheckTime = now

		// Проверяем, попадает ли цена ордера в диапазон свечи
		// Для ордеров на покупку: исполняем, если цена ордера >= минимальной цены свечи
		// Для ордеров на продажу: исполняем, если цена ордера <= максимальной цены свечи
		priceInRange := false
		if order.Side == "BUY" && order.Price >= candle.Low {
			priceInRange = true
		} else if order.Side == "SELL" && order.Price <= candle.High {
			priceInRange = true
		}

		if priceInRange {
			// Исполняем ордер по цене ордера (лимитный ордер)
			order.Status = "FILLED"
			order.ExecutedQty = order.Quantity
			order.UpdatedAt = now

			// Обновляем балансы
			baseAsset := order.Symbol[:3]  // Упрощенное извлечение базового актива
			quoteAsset := order.Symbol[3:] // Упрощенное извлечение котируемого актива

			if order.Side == "BUY" {
				// Покупка: уменьшаем котируемую валюту, увеличиваем базовую
				b.updateBalance(quoteAsset, -order.Price*order.Quantity)
				b.updateBalance(baseAsset, order.Quantity)
			} else {
				// Продажа: уменьшаем базовую валюту, увеличиваем котируемую
				b.updateBalance(baseAsset, -order.Quantity)
				b.updateBalance(quoteAsset, order.Price*order.Quantity)
			}

			// Добавляем в историю сделок
			b.trades = append(b.trades, order)
			// Оставляем ордер в карте с обновленным статусом FILLED
			// для возможности последующего запроса по ID
		}
	}
}

// SubscribeToOrderBook подписывается на обновления стакана
func (b *TestBroker) SubscribeToOrderBook(symbol string) (chan types.OrderBook, error) {
	b.mu.Lock()
	defer b.mu.Unlock()

	ch := make(chan types.OrderBook, 100)

	if _, exists := b.orderbookSubs[symbol]; !exists {
		b.orderbookSubs[symbol] = make(map[chan types.OrderBook]struct{})
	}

	b.orderbookSubs[symbol][ch] = struct{}{}

	// Запускаем генератор тестовых данных, если это первая подписка
	if len(b.orderbookSubs[symbol]) == 1 {
		b.wg.Add(1)
		go b.generateTestOrderBook(symbol, ch)
	}

	return ch, nil
}

// UnsubscribeFromOrderBook отписывается от обновлений стакана
func (b *TestBroker) UnsubscribeFromOrderBook(symbol string, ch chan types.OrderBook) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if subs, exists := b.orderbookSubs[symbol]; exists {
		if _, ok := subs[ch]; ok {
			delete(subs, ch)
			close(ch)
			if len(subs) == 0 {
				delete(b.orderbookSubs, symbol)
			}
			return nil
		}
	}
	return fmt.Errorf("subscription not found")
}

// generateTestOrderBook генерирует тестовый стакан
func (b *TestBroker) generateTestOrderBook(symbol string, ch chan<- types.OrderBook) {
	defer b.wg.Done()

	ticker := time.NewTicker(500 * time.Millisecond) // Обновляем 2 раза в секунду
	defer ticker.Stop()

	// Начальная цена
	price := 50000.0

	for {
		select {
		case <-b.ctx.Done():
			close(ch)
			return
		case <-ticker.C:
			// Генерируем случайное изменение цены
			change := (rand.Float64() - 0.5) * 0.001 // ±0.05%
			price = price * (1 + change)

			// Генерируем тестовый стакан
			orderBook := types.OrderBook{
				Time: time.Now().UnixMilli(),
			}

			// Генерируем биды и аски вокруг текущей цены
			for i := 1; i <= 10; i++ {
				// Аски (ордера на продажу)
				askPrice := price * (1 + 0.001*float64(i))
				askQty := 0.1 + rand.Float64()*0.9 // 0.1-1.0
				orderBook.Asks = append(orderBook.Asks, types.BookEntry{
					Price:    askPrice,
					Quantity: askQty,
				})

				// Биды (ордера на покупку)
				bidPrice := price * (1 - 0.001*float64(i))
				bidQty := 0.1 + rand.Float64()*0.9 // 0.1-1.0
				orderBook.Bids = append(orderBook.Bids, types.BookEntry{
					Price:    bidPrice,
					Quantity: bidQty,
				})
			}

			// Отправляем обновление стакана
			select {
			case ch <- orderBook:
			default:
				log.Println("order book channel full, dropping update")
			}
		}
	}
}

// Close завершает работу брокера
func (b *TestBroker) Close() error {
	// Отменяем контекст для остановки горутин
	b.cancelFunc()

	// Закрываем все каналы подписок на свечи
	for symbol, subs := range b.candleSubs {
		for ch := range subs {
			close(ch)
		}
		delete(b.candleSubs, symbol)
	}

	// Закрываем все каналы подписок на стакан
	for symbol, subs := range b.orderbookSubs {
		for ch := range subs {
			close(ch)
		}
		delete(b.orderbookSubs, symbol)
	}

	// Ждем завершения всех горутин
	done := make(chan struct{})
	go func() {
		b.wg.Wait()
		close(done)
	}()

	// Таймаут на завершение
	select {
	case <-done:
	case <-time.After(5 * time.Second):
		log.Println("warning: timeout waiting for goroutines to finish")
	}

	return nil
}
