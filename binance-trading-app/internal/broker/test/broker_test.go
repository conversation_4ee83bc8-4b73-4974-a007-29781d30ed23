package test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"binance-trading-app/internal/broker/types"
)

func TestTestBroker_AutoExecution(t *testing.T) {
	// Setup test broker with test mode enabled
	broker := NewTestBroker()
	ctx := context.Background()

	// Connect and initialize the broker
	err := broker.Connect(ctx)
	require.NoError(t, err)

	// Test buy order execution
	t.Run("BuyOrderExecution", func(t *testing.T) {
		// Place a buy limit order at 50000 USDT
		order, err := broker.CreateOrder(ctx, "BTCUSDT", "BUY", "LIMIT", 0.1, 50000.0)
		require.NoError(t, err)
		require.NotNil(t, order)

		// Get initial balance
		initialBalance, err := broker.GetBalance(ctx, "USDT")
		require.NoError(t, err)

		initialUSDT := initialBalance.Free

		// Generate a candle that includes the buy price
		candle := types.Candle{
			OpenTime:  time.Now().UnixMilli(),
			CloseTime: time.Now().Add(time.Minute).UnixMilli(),
			Open:      49000,
			High:      51000,
			Low:       48000,
			Close:     50500,
			Volume:    10,
		}


		// Check for order execution with the test candle
		broker.checkOrdersForExecution(candle)


		// Verify the order was filled
		filledOrder, err := broker.GetOrder(ctx, order.ID, "BTCUSDT")
		require.NoError(t, err)
		require.Equal(t, "FILLED", filledOrder.Status)
		require.Equal(t, order.Quantity, filledOrder.ExecutedQty)

		// Verify balances were updated correctly
		newBalance, err := broker.GetBalance(ctx, "USDT")
		require.NoError(t, err)
		// USDT should decrease by order.Price * order.Quantity
		expectedUSDT := initialUSDT - (order.Price * order.Quantity)
		assert.InDelta(t, expectedUSDT, newBalance.Free, 0.001, "USDT balance should decrease by order amount")

		// Verify BTC balance increased
		btcBalance, err := broker.GetBalance(ctx, "BTC")
		require.NoError(t, err)
		assert.Greater(t, btcBalance.Free, 0.0, "BTC balance should increase")
	})

	// Test sell order execution
	t.Run("SellOrderExecution", func(t *testing.T) {
		// First, ensure we have some BTC to sell
		btcBalance, err := broker.GetBalance(ctx, "BTC")
		require.NoError(t, err)
		if btcBalance.Free < 0.1 {
			// Update the balance directly in the test
			broker.mu.Lock()
			broker.balances[types.Asset("BTC")] = types.Balance{
				Asset: "BTC",
				Free:  0.1,
				Locked: 0,
			}
			broker.mu.Unlock()
		}

		// Place a sell limit order at 51000 USDT
		order, err := broker.CreateOrder(ctx, "BTCUSDT", "SELL", "LIMIT", 0.05, 51000.0)
		require.NoError(t, err)
		require.NotNil(t, order)

		// Get initial balances
		initialBTC, err := broker.GetBalance(ctx, "BTC")
		require.NoError(t, err)
		initialUSDT, err := broker.GetBalance(ctx, "USDT")
		require.NoError(t, err)

		// Generate a candle that includes the sell price
		candle := types.Candle{
			OpenTime:  time.Now().UnixMilli(),
			CloseTime: time.Now().Add(time.Minute).UnixMilli(),
			Open:      50000,
			High:      52000,  // Above our sell price
			Low:       49000,
			Close:     51500,
			Volume:    10,
		}


		// Check for order execution with the test candle
		broker.checkOrdersForExecution(candle)

		// Verify the order was filled
		filledOrder, err := broker.GetOrder(ctx, order.ID, "BTCUSDT")
		require.NoError(t, err)
		require.Equal(t, "FILLED", filledOrder.Status)

		// Verify balances were updated correctly
		newBTC, err := broker.GetBalance(ctx, "BTC")
		require.NoError(t, err)
		assert.InDelta(t, initialBTC.Free-order.Quantity, newBTC.Free, 0.0001, "BTC balance should decrease by order quantity")

		newUSDT, err := broker.GetBalance(ctx, "USDT")
		require.NoError(t, err)
		expectedUSDT := initialUSDT.Free + (order.Price * order.Quantity)
		assert.InDelta(t, expectedUSDT, newUSDT.Free, 0.001, "USDT balance should increase by order value")
	})

	// Test order not executed when price not in range
	t.Run("OrderNotExecutedWhenPriceNotInRange", func(t *testing.T) {
		// Place a buy order at 40000 USDT (below current market)
		order, err := broker.CreateOrder(ctx, "BTCUSDT", "BUY", "LIMIT", 0.1, 40000.0)
		require.NoError(t, err)

		// Generate a candle that doesn't include the buy price
		candle := types.Candle{
			OpenTime:  time.Now().UnixMilli(),
			CloseTime: time.Now().Add(time.Minute).UnixMilli(),
			Open:      45000,
			High:      49000,  // Below our buy price of 50000
			Low:       44000,
			Close:     48000,
			Volume:    10,
		}


		// Check for order execution with the test candle
		broker.checkOrdersForExecution(candle)


		// Verify the order is still open
		openOrder, err := broker.GetOrder(ctx, order.ID, "BTCUSDT")
		require.NoError(t, err)
		require.Equal(t, "NEW", openOrder.Status, "Order should still be open")
	})

	// Cleanup
	err = broker.Close()
	require.NoError(t, err)
}
