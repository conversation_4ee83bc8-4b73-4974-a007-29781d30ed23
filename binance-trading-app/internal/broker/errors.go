package broker

import (
	"errors"
	"fmt"
)

// Общие ошибки брокера
var (
	ErrNotConnected        = errors.New("not connected to broker")
	ErrAlreadyConnected    = errors.New("already connected to broker")
	ErrConnectionFailed    = errors.New("connection to broker failed")
	ErrInvalidSymbol       = errors.New("invalid symbol")
	ErrInvalidOrder        = errors.New("invalid order")
	ErrOrderNotFound       = errors.New("order not found")
	ErrOrderFailed         = errors.New("order failed")
	ErrInsufficientFunds   = errors.New("insufficient funds")
	ErrRateLimitExceeded   = errors.New("rate limit exceeded")
	ErrNotImplemented      = errors.New("not implemented")
	ErrSubscriptionFailed  = errors.New("subscription failed")
	ErrUnsubscriptionFailed = errors.New("unsubscription failed")
	ErrInvalidConfig       = errors.New("invalid broker configuration")
	ErrUnsupportedBrokerType = errors.New("unsupported broker type")
	ErrInvalidQuantity     = errors.New("invalid quantity")
	ErrInvalidPrice        = errors.New("invalid price")
)

// BrokerError представляет ошибку брокера
type BrokerError struct {
	msg string
}

// NewBrokerError создает новую ошибку брокера
func NewBrokerError(msg string) error {
	return &BrokerError{msg: msg}
}

// Error возвращает текстовое представление ошибки
func (e *BrokerError) Error() string {
	return e.msg
}

// Errorf создает новую форматированную ошибку брокера
func Errorf(format string, args ...interface{}) error {
	return NewBrokerError(fmt.Sprintf(format, args...))
}

// Is проверяет, является ли ошибка ошибкой брокера
func Is(err error, target error) bool {
	e, ok := err.(*BrokerError)
	if !ok {
		return false
	}
	t, ok := target.(*BrokerError)
	if !ok {
		return false
	}
	return e.msg == t.msg
}
