package broker

import (
	"context"

	"binance-trading-app/internal/broker/types"
)

// BrokerType является алиасом для types.BrokerType для обратной совместимости
type BrokerType = types.BrokerType

// Константы типов брокеров
const (
	BrokerTypeBinance = types.BrokerTypeBinance
)

// Алиасы типов для обратной совместимости
type (
	Symbol    = types.Symbol
	Balance   = types.Balance
	Candle    = types.Candle
	OrderBook = types.OrderBook
	Order     = types.Order
	Config    = types.Config
)

// Broker определяет общий интерфейс для работы с брокерами
type Broker interface {
	// Подключение и аутентификация
	Connect(ctx context.Context) error
	IsConnected() bool
	SetAPIKey(apiKey, apiSecret string, isTestnet bool) error
	
	// Информация о бирже
	GetExchangeInfo(ctx context.Context) ([]types.Symbol, error)
	
	// Работа с аккаунтом
	GetAccountInfo(ctx context.Context) ([]types.Balance, error)
	GetBalance(ctx context.Context, asset string) (*types.Balance, error)
	
	// Работа с ордерами
	CreateOrder(ctx context.Context, symbol, side, orderType string, quantity, price float64) (*types.Order, error)
	GetOrder(ctx context.Context, orderID, symbol string) (*types.Order, error)
	CancelOrder(ctx context.Context, orderID, symbol string) error
	GetOpenOrders(ctx context.Context, symbol string) ([]*types.Order, error)
	
	// Подписка на рыночные данные
	SubscribeToCandles(symbol, interval string) (chan []types.Candle, error) // interval: "1m", "5m", "1h" и т.д.
	SubscribeToOrderBook(symbol string) (chan types.OrderBook, error)
	
	// Закрытие соединения
	Close() error
}
