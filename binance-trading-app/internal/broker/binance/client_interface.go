package binance

import (
	"context"

	"github.com/adshao/go-binance/v2"
	"github.com/adshao/go-binance/v2/futures"
)

// Service interfaces
type (
	PingService interface {
		Do(ctx context.Context) error
	}

	ServerTimeService interface {
		Do(ctx context.Context) (int64, error)
	}

	ExchangeInfoService interface {
		Do(ctx context.Context) (*binance.ExchangeInfo, error)
	}

	GetAccountService interface {
		Do(ctx context.Context) (*binance.Account, error)
	}

	CreateOrderService interface {
		Symbol(symbol string) CreateOrderService
		Side(side string) CreateOrderService
		Type(orderType string) CreateOrderService
		Quantity(quantity string) CreateOrderService
		Price(price string) CreateOrderService
		TimeInForce(timeInForce string) CreateOrderService
		NewOrderResponseType(newOrderResponseType string) CreateOrderService
		Do(ctx context.Context) (*binance.CreateOrderResponse, error)
	}

	GetOrderService interface {
		Symbol(symbol string) GetOrderService
		OrderID(orderID string) GetOrderService
		Do(ctx context.Context) (*binance.Order, error)
	}

	CancelOrderService interface {
		Symbol(symbol string) CancelOrderService
		OrderID(orderID string) CancelOrderService
		Do(ctx context.Context) (*binance.CancelOrderResponse, error)
	}

	ListOpenOrdersService interface {
		Symbol(symbol string) ListOpenOrdersService
		Do(ctx context.Context) ([]*binance.Order, error)
	}
)

// BinanceClient defines the common interface for both Spot and Futures clients
type BinanceClient interface {
	// Common methods for both Spot and Futures
	NewPingService() PingService
	NewServerTimeService() ServerTimeService

	// Account related methods
	NewGetAccountService() GetAccountService

	// Order related methods
	NewCreateOrderService() CreateOrderService
	NewGetOrderService() GetOrderService
	NewCancelOrderService() CancelOrderService
	NewListOpenOrdersService() ListOpenOrdersService

	// Exchange info
	NewExchangeInfoService() ExchangeInfoService
}

// spotClient implements BinanceClient for Spot API
type spotClient struct {
	client *binance.Client
}

func (s *spotClient) NewPingService() PingService {
	return &spotPingService{svc: s.client.NewPingService()}
}

func (s *spotClient) NewServerTimeService() ServerTimeService {
	return &spotServerTimeService{svc: s.client.NewServerTimeService()}
}

func (s *spotClient) NewGetAccountService() GetAccountService {
	return &spotGetAccountService{svc: s.client.NewGetAccountService()}
}

func (s *spotClient) NewCreateOrderService() CreateOrderService {
	return &spotCreateOrderService{svc: s.client.NewCreateOrderService()}
}

func (s *spotClient) NewGetOrderService() GetOrderService {
	return &spotGetOrderService{svc: s.client.NewGetOrderService()}
}

func (s *spotClient) NewCancelOrderService() CancelOrderService {
	return &spotCancelOrderService{svc: s.client.NewCancelOrderService()}
}

func (s *spotClient) NewListOpenOrdersService() ListOpenOrdersService {
	return &spotListOpenOrdersService{svc: s.client.NewListOpenOrdersService()}
}

func (s *spotClient) NewExchangeInfoService() ExchangeInfoService {
	return &spotExchangeInfoService{svc: s.client.NewExchangeInfoService()}
}

// futuresClient implements BinanceClient for Futures API
type futuresClient struct {
	client *futures.Client
}

func (f *futuresClient) NewPingService() PingService {
	return &futuresPingService{svc: f.client.NewPingService()}
}

func (f *futuresClient) NewServerTimeService() ServerTimeService {
	return &futuresServerTimeService{svc: f.client.NewServerTimeService()}
}

func (f *futuresClient) NewGetAccountService() GetAccountService {
	return &futuresGetAccountService{svc: f.client.NewGetAccountService()}
}

func (f *futuresClient) NewCreateOrderService() CreateOrderService {
	return &futuresCreateOrderService{svc: f.client.NewCreateOrderService()}
}

func (f *futuresClient) NewGetOrderService() GetOrderService {
	return &futuresGetOrderService{svc: f.client.NewGetOrderService()}
}

func (f *futuresClient) NewCancelOrderService() CancelOrderService {
	return &futuresCancelOrderService{svc: f.client.NewCancelOrderService()}
}

func (f *futuresClient) NewListOpenOrdersService() ListOpenOrdersService {
	return &futuresListOpenOrdersService{svc: f.client.NewListOpenOrdersService()}
}

func (f *futuresClient) NewExchangeInfoService() ExchangeInfoService {
	return &futuresExchangeInfoService{svc: f.client.NewExchangeInfoService()}
}
