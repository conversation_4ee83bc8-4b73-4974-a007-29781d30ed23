package binance

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2"
	"github.com/adshao/go-binance/v2/futures"

	"binance-trading-app/internal/broker"
	"binance-trading-app/internal/broker/types"
)

// BinanceBroker реализует интерфейс broker.Broker для Binance
// Управляет подключением к API Binance и обработкой запросов
// Поддерживает как Spot, так и Futures торговлю
type BinanceBroker struct {
	APIKey       string
	APISecret    string
	TestNet      bool
	ExchangeType types.ExchangeType
	client       BinanceClient
	wsClient     interface{} // WebSocket клиент
	connected    bool
	mu           sync.Mutex
	
	// WebSocket connections
	wsStopChan      chan struct{}
	wsDoneChan      chan struct{}
	wsSubscriptions map[string]func()
}

// Connect устанавливает соединение с Binance API
func (b *BinanceBroker) Connect(ctx context.Context) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if b.connected {
		return broker.ErrAlreadyConnected
	}

	// Инициализируем клиент
	b.client = futures.NewClient(b.APIKey, b.APISecret)
	if b.TestNet {
		futures.UseTestnet = true
	}

	// Проверяем соединение
	err := b.client.NewPingService().Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to ping Binance: %w", err)
	}

	log.Println("Successfully connected to Binance")
	b.connected = true
	return nil
}

// SetAPIKey обновляет API ключи и переподключается, если необходимо
func (b *BinanceBroker) SetAPIKey(apiKey, apiSecret string, isTestnet bool) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	wasConnected := b.connected
	
	// Закрываем существующее соединение, если оно активно
	if wasConnected {
		b.client = nil
		b.connected = false
	}

	// Обновляем учетные данные
	b.APIKey = apiKey
	b.APISecret = apiSecret
	b.TestNet = isTestnet

	// Если было подключение, переподключаемся с новыми учетными данными
	if wasConnected {
		b.client = futures.NewClient(b.APIKey, b.APISecret)
		if b.TestNet {
			futures.UseTestnet = true
		}

		// Проверяем соединение
		if err := b.client.NewPingService().Do(context.Background()); err != nil {
			return fmt.Errorf("failed to reconnect with new API keys: %w", err)
		}
		b.connected = true
	}

	return nil
}

// IsConnected возвращает статус соединения
func (b *BinanceBroker) IsConnected() bool {
	b.mu.Lock()
	defer b.mu.Unlock()
	return b.connected
}

// Close закрывает соединение
func (b *BinanceBroker) Close() error {
	b.mu.Lock()
	defer b.mu.Unlock()

	// Здесь можно добавить закрытие WebSocket соединений
	b.connected = false
	return nil
}

// GetExchangeInfo возвращает информацию о торговых парах
func (b *BinanceBroker) GetExchangeInfo(ctx context.Context) ([]broker.Symbol, error) {
	if !b.IsConnected() {
		return nil, broker.ErrNotConnected
	}

	exchangeInfo, err := b.client.NewExchangeInfoService().Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get exchange info: %w", err)
	}

	var symbols []broker.Symbol
	for _, s := range exchangeInfo.Symbols {
		var minPrice, maxPrice, tickSize, minQty, maxQty, stepSize, minNotional float64

		// Парсим фильтры
		for _, filter := range s.Filters {
			// Convert filter to map[string]interface{} using JSON marshaling
			jsonBytes, err := json.Marshal(filter)
			if err != nil {
				continue
			}

			var filterMap map[string]interface{}
			if err := json.Unmarshal(jsonBytes, &filterMap); err != nil {
				continue
			}

			filterType, ok := filterMap["filterType"].(string)
			if !ok {
				continue
			}

			switch filterType {
			case "PRICE_FILTER":
				if v, ok := filterMap["minPrice"].(string); ok {
					minPrice = mustParseFloat(v)
				}
				if v, ok := filterMap["maxPrice"].(string); ok {
					maxPrice = mustParseFloat(v)
				}
				if v, ok := filterMap["tickSize"].(string); ok {
					tickSize = mustParseFloat(v)
				}
			case "LOT_SIZE":
				if v, ok := filterMap["minQty"].(string); ok {
					minQty = mustParseFloat(v)
				}
				if v, ok := filterMap["maxQty"].(string); ok {
					maxQty = mustParseFloat(v)
				}
				if v, ok := filterMap["stepSize"].(string); ok {
					stepSize = mustParseFloat(v)
				}
			case "MIN_NOTIONAL":
				if v, ok := filterMap["minNotional"].(string); ok {
					minNotional = mustParseFloat(v)
				}
			}
		}

		symbols = append(symbols, broker.Symbol{
			ID:          s.Symbol,
			BaseAsset:   s.BaseAsset,
			QuoteAsset:  s.QuoteAsset,
			MinPrice:    minPrice,
			MaxPrice:    maxPrice,
			TickSize:    tickSize,
			MinQty:      minQty,
			MaxQty:      maxQty,
			StepSize:    stepSize,
			MinNotional: minNotional,
		})
	}

	return symbols, nil
}

// GetAccountInfo возвращает информацию о балансе аккаунта
func (b *BinanceBroker) GetAccountInfo(ctx context.Context) ([]types.Balance, error) {
	if !b.IsConnected() {
		return nil, broker.ErrNotConnected
	}

	account, err := b.client.NewGetAccountService().Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get account info: %w", err)
	}

	var balances []types.Balance
	// For Binance Futures, we'll use the Assets slice which contains wallet balances
	for _, asset := range account.Assets {
		// Convert the wallet balance to float
		walletBalance, err := strconv.ParseFloat(asset.WalletBalance, 64)
		if err != nil {
			continue
		}

		// Only include assets with non-zero balance
		if walletBalance > 0 {
			balances = append(balances, types.Balance{
				Asset:  asset.Asset,
				Free:   walletBalance,
				Locked: 0, // Binance futures doesn't have a direct "locked" field in Assets
			})
		}
	}

	return balances, nil
}

// GetBalance возвращает баланс по указанному активу
func (b *BinanceBroker) GetBalance(ctx context.Context, asset string) (*broker.Balance, error) {
	balances, err := b.GetAccountInfo(ctx)
	if err != nil {
		return nil, err
	}

	for _, balance := range balances {
		if balance.Asset == asset {
			return &balance, nil
		}
	}

	return &broker.Balance{Asset: asset, Free: 0, Locked: 0}, nil
}

// CreateOrder создает новый ордер
func (b *BinanceBroker) CreateOrder(ctx context.Context, symbol, side, orderType string, quantity, price float64) (*broker.Order, error) {
	if !b.IsConnected() {
		return nil, broker.ErrNotConnected
	}

	service := b.client.NewCreateOrderService()
	service.Symbol(symbol)
	service.Side(side)
	service.Type(futures.OrderType(orderType))
	service.Quantity(fmt.Sprintf("%.8f", quantity))

	if orderType == "LIMIT" {
		service.TimeInForce(futures.TimeInForceTypeGTC)
		service.Price(fmt.Sprintf("%.8f", price))
	}

	order, err := service.Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	// For new orders, use the current time as created/updated time
	currentTime := time.Now().UnixNano() / int64(time.Millisecond)
	
	return &broker.Order{
		ID:           fmt.Sprintf("%d", order.OrderID),
		Symbol:       order.Symbol,
		Side:         string(order.Side),
		Type:         string(order.Type),
		Price:        mustParseFloat(order.Price),
		Quantity:     mustParseFloat(order.OrigQuantity),
		ExecutedQty:  mustParseFloat(order.ExecutedQuantity),
		Status:       string(order.Status),
		TimeInForce:  string(order.TimeInForce),
		CreatedAt:    currentTime,
		UpdatedAt:    currentTime,
	}, nil
}

// GetOrder возвращает информацию об ордере
func (b *BinanceBroker) GetOrder(ctx context.Context, orderID, symbol string) (*broker.Order, error) {
	if !b.IsConnected() {
		return nil, broker.ErrNotConnected
	}

	order, err := b.client.NewGetOrderService().
		Symbol(symbol).
		OrderID(mustParseInt64(orderID)).
		Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	// Use current time as fallback for timestamps if not available
	currentTime := time.Now().UnixNano() / int64(time.Millisecond)
	createdAt := currentTime
	updatedAt := currentTime

	// If we have update time, use it for both created and updated
	if order.UpdateTime > 0 {
		createdAt = order.UpdateTime
		updatedAt = order.UpdateTime
	}

	return &broker.Order{
		ID:           fmt.Sprintf("%d", order.OrderID),
		Symbol:       order.Symbol,
		Side:         string(order.Side),
		Type:         string(order.Type),
		Price:        mustParseFloat(order.Price),
		Quantity:     mustParseFloat(order.OrigQuantity),
		ExecutedQty:  mustParseFloat(order.ExecutedQuantity),
		Status:       string(order.Status),
		TimeInForce:  string(order.TimeInForce),
		CreatedAt:    createdAt,
		UpdatedAt:    updatedAt,
	}, nil
}

// CancelOrder отменяет ордер
func (b *BinanceBroker) CancelOrder(ctx context.Context, orderID, symbol string) error {
	if !b.IsConnected() {
		return broker.ErrNotConnected
	}

	_, err := b.client.NewCancelOrderService().
		Symbol(symbol).
		OrderID(mustParseInt64(orderID)).
		Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to cancel order: %w", err)
	}

	return nil
}

// GetOpenOrders возвращает список открытых ордеров
func (b *BinanceBroker) GetOpenOrders(ctx context.Context, symbol string) ([]*broker.Order, error) {
	if !b.IsConnected() {
		return nil, broker.ErrNotConnected
	}

	orders, err := b.client.NewListOpenOrdersService().
		Symbol(symbol).
		Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get open orders: %w", err)
	}

	var result []*broker.Order
	for _, order := range orders {
		result = append(result, &broker.Order{
			ID:           fmt.Sprintf("%d", order.OrderID),
			Symbol:       order.Symbol,
			Side:         string(order.Side),
			Type:         string(order.Type),
			Price:        mustParseFloat(order.Price),
			Quantity:     mustParseFloat(order.OrigQuantity),
			ExecutedQty:  mustParseFloat(order.ExecutedQuantity),
			Status:       string(order.Status),
			TimeInForce:  string(order.TimeInForce),
			CreatedAt:    order.Time,
			UpdatedAt:    order.UpdateTime,
		})
	}

	return result, nil
}

// SubscribeToCandles implements the Broker interface.
func (b *BinanceBroker) SubscribeToCandles(symbol, interval string) (chan []broker.Candle, error) {
	candlesChan := make(chan []broker.Candle, 100)

	// Create context for unsubscription
	ctx, cancel := context.WithCancel(context.Background())

	// Create a unique key for this subscription
	subKey := fmt.Sprintf("kline_%s_%s", symbol, interval)

	// Lock for thread-safe access to subscriptions map
	b.mu.Lock()

	// Initialize the subscriptions map if needed
	if b.wsSubscriptions == nil {
		b.wsSubscriptions = make(map[string]func())
	}

	// Check if there's an existing subscription and cancel it
	if existingCancel, exists := b.wsSubscriptions[subKey]; exists {
		existingCancel()
	}

	// Create handler for kline events
	handler := func(event *futures.WsKlineEvent) {
		candle := broker.Candle{
			OpenTime:  event.Kline.StartTime,
			Open:      mustParseFloat(event.Kline.Open),
			High:      mustParseFloat(event.Kline.High),
			Low:       mustParseFloat(event.Kline.Low),
			Close:     mustParseFloat(event.Kline.Close),
			Volume:    mustParseFloat(event.Kline.Volume),
			CloseTime: event.Kline.EndTime,
		}

		// Send the candle update
		select {
		case candlesChan <- []broker.Candle{candle}:
		case <-ctx.Done():
			// Context was canceled, stop sending updates
			return
		default:
			log.Println("Warning: candles channel is full, dropping update")
		}
	}

	errHandler := func(err error) {
		log.Printf("WebSocket error: %v", err)
	}

	// Subscribe to kline stream
	doneC, stopC, err := futures.WsKlineServe(symbol, interval, handler, errHandler)
	if err != nil {
		cancel()
		b.mu.Unlock()
		close(candlesChan)
		return nil, fmt.Errorf("failed to subscribe to kline: %w", err)
	}

	// Store the cancel function for this subscription
	b.wsSubscriptions[subKey] = func() {
		close(stopC)
		cancel()
	}

	// Start a goroutine to clean up when the context is done
	go func() {
		select {
		case <-ctx.Done():
		case <-doneC:
		}
		b.mu.Lock()
		delete(b.wsSubscriptions, subKey)
		b.mu.Unlock()
		close(candlesChan)
	}()

	b.mu.Unlock()

	return candlesChan, nil
}

// mustParseFloat is a helper function to parse string to float64, panicking on error
func mustParseFloat(s string) float64 {
	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		panic(err)
	}
	return f
}

// SubscribeToOrderBook implements the Broker interface to subscribe to order book updates.
func (b *BinanceBroker) SubscribeToOrderBook(symbol string) (chan types.OrderBook, error) {
	// Create channel for order book updates
	orderBookChan := make(chan types.OrderBook, 100)

	// Create context for unsubscription
	ctx, cancel := context.WithCancel(context.Background())

	// Create a unique key for this subscription
	subKey := fmt.Sprintf("depth_%s", symbol)

	// Lock for thread-safe access to subscriptions map
	b.mu.Lock()

	// Initialize the subscriptions map if needed
	if b.wsSubscriptions == nil {
		b.wsSubscriptions = make(map[string]func())
	}

	// Check if there's an existing subscription and cancel it
	if existingCancel, exists := b.wsSubscriptions[subKey]; exists {
		existingCancel()
	}

	// Create order book handler
	handler := func(event *futures.WsDepthEvent) {
		bids := make([]types.BookEntry, 0, len(event.Bids))
		for _, bid := range event.Bids {
			price := mustParseFloat(bid.Price)
			quantity := mustParseFloat(bid.Quantity)
			bids = append(bids, types.BookEntry{
				Price:    price,
				Quantity: quantity,
			})
		}

		asks := make([]types.BookEntry, 0, len(event.Asks))
		for _, ask := range event.Asks {
			price := mustParseFloat(ask.Price)
			quantity := mustParseFloat(ask.Quantity)
			asks = append(asks, types.BookEntry{
				Price:    price,
				Quantity: quantity,
			})
		}

		select {
		case orderBookChan <- types.OrderBook{
			Bids:         bids,
			Asks:         asks,
			Time:         event.Time,
			LastUpdateID: event.LastUpdateID,
		}:
		default:
			log.Println("order book channel is full, dropping update")
		}
	}

	// Error handler
	errHandler := func(err error) {
		log.Printf("WebSocket error: %v", err)
	}

	// Subscribe to diff depth updates with 100ms interval
	doneC, stopC, err := futures.WsDiffDepthServeWithRate(symbol, 100*time.Millisecond, handler, errHandler)
	if err != nil {
		cancel()
		b.mu.Unlock()
		close(orderBookChan)
		return nil, fmt.Errorf("failed to subscribe to depth: %w", err)
	}

	// Store the cancel function for this subscription
	b.wsSubscriptions[subKey] = func() {
		close(stopC)
		cancel()
	}

	// Start a goroutine to clean up when the context is done
	go func() {
		select {
		case <-ctx.Done():
		case <-doneC:
		}
		b.mu.Lock()
		delete(b.wsSubscriptions, subKey)
		b.mu.Unlock()
		close(orderBookChan)
	}()

	b.mu.Unlock()

	return orderBookChan, nil
}
