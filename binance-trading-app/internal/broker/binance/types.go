package binance

import (
	"binance-trading-app/internal/broker"
)

// NewBinanceBroker создает новый экземпляр BinanceBroker
func NewBinanceBroker(apiKey, apiSecret string, testNet bool) *BinanceBroker {
	return &BinanceBroker{
		APIKey:    apiKey,
		APISecret: apiSecret,
		TestNet:   testNet,
	}
}

// init регистрирует фабричную функцию Binance брокера при импорте пакета
func init() {
	broker.Register(broker.BrokerTypeBinance, func(config broker.Config) (broker.Broker, error) {
		if config.APIKey == "" || config.APISecret == "" {
			return nil, broker.ErrInvalidConfig
		}
		return NewBinanceBroker(config.APIKey, config.APISecret, config.TestNet), nil
	})
}
