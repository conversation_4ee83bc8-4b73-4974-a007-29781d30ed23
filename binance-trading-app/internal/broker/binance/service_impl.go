// Package binance implements the Binance broker adapter.
package binance

import (
	"context"
	"fmt"
	"reflect"
	"strconv"

	"github.com/adshao/go-binance/v2"
	"github.com/adshao/go-binance/v2/futures"
)

// spotPingService wraps binance.PingService
type spotPingService struct {
	svc *binance.PingService
}

func (s *spotPingService) Do(ctx context.Context) error {
	_, err := s.svc.Do(ctx)
	return err
}

// spotServerTimeService wraps binance.ServerTimeService
type spotServerTimeService struct {
	svc *binance.ServerTimeService
}

func (s *spotServerTimeService) Do(ctx context.Context) (int64, error) {
	return s.svc.Do(ctx)
}

// spotExchangeInfoService wraps binance.ExchangeInfoService
type spotExchangeInfoService struct {
	svc *binance.ExchangeInfoService
}

func (s *spotExchangeInfoService) Do(ctx context.Context) (*binance.ExchangeInfo, error) {
	return s.svc.Do(ctx)
}

// spotGetAccountService wraps binance.GetAccountService
type spotGetAccountService struct {
	svc *binance.GetAccountService
}

func (s *spotGetAccountService) Do(ctx context.Context) (*binance.Account, error) {
	return s.svc.Do(ctx)
}

// spotCreateOrderService wraps binance.CreateOrderService
type spotCreateOrderService struct {
	svc *binance.CreateOrderService
}

func (s *spotCreateOrderService) Symbol(symbol string) CreateOrderService {
	s.svc.Symbol(symbol)
	return s
}

func (s *spotCreateOrderService) Side(side string) CreateOrderService {
	s.svc.Side(binance.SideType(side))
	return s
}

func (s *spotCreateOrderService) Type(orderType string) CreateOrderService {
	s.svc.Type(binance.OrderType(orderType))
	return s
}

func (s *spotCreateOrderService) Quantity(quantity string) CreateOrderService {
	s.svc.Quantity(quantity)
	return s
}

func (s *spotCreateOrderService) Price(price string) CreateOrderService {
	s.svc.Price(price)
	return s
}

func (s *spotCreateOrderService) TimeInForce(timeInForce string) CreateOrderService {
	s.svc.TimeInForce(binance.TimeInForceType(timeInForce))
	return s
}

func (s *spotCreateOrderService) NewOrderResponseType(newOrderResponseType string) CreateOrderService {
	// Note: Spot API doesn't have NewOrderResponseType in CreateOrderService
	// This is a no-op for spot API
	return s
}

func (s *spotCreateOrderService) Do(ctx context.Context) (*binance.CreateOrderResponse, error) {
	return s.svc.Do(ctx)
}

// spotGetOrderService wraps binance.GetOrderService
type spotGetOrderService struct {
	svc *binance.GetOrderService
}

func (s *spotGetOrderService) Symbol(symbol string) GetOrderService {
	s.svc.Symbol(symbol)
	return s
}

func (s *spotGetOrderService) OrderID(orderID string) GetOrderService {
	s.svc.OrderID(mustParseUint64(orderID))
	return s
}

func (s *spotGetOrderService) Do(ctx context.Context) (*binance.Order, error) {
	return s.svc.Do(ctx)
}

// spotCancelOrderService wraps binance.CancelOrderService
type spotCancelOrderService struct {
	svc *binance.CancelOrderService
}

func (s *spotCancelOrderService) Symbol(symbol string) CancelOrderService {
	s.svc.Symbol(symbol)
	return s
}

func (s *spotCancelOrderService) OrderID(orderID string) CancelOrderService {
	s.svc.OrderID(mustParseUint64(orderID))
	return s
}

func (s *spotCancelOrderService) Do(ctx context.Context) (*binance.CancelOrderResponse, error) {
	return s.svc.Do(ctx)
}

// spotListOpenOrdersService wraps binance.ListOpenOrdersService
type spotListOpenOrdersService struct {
	svc *binance.ListOpenOrdersService
}

func (s *spotListOpenOrdersService) Symbol(symbol string) ListOpenOrdersService {
	s.svc.Symbol(symbol)
	return s
}

func (s *spotListOpenOrdersService) Do(ctx context.Context) ([]*binance.Order, error) {
	return s.svc.Do(ctx)
}

// futuresPingService wraps futures.PingService
type futuresPingService struct {
	svc *futures.PingService
}

func (s *futuresPingService) Do(ctx context.Context) error {
	_, err := s.svc.Do(ctx)
	return err
}

// futuresServerTimeService wraps futures.ServerTimeService
type futuresServerTimeService struct {
	svc *futures.ServerTimeService
}

func (s *futuresServerTimeService) Do(ctx context.Context) (int64, error) {
	return s.svc.Do(ctx)
}

// futuresExchangeInfoService wraps futures.ExchangeInfoService
type futuresExchangeInfoService struct {
	svc *futures.ExchangeInfoService
}

func (s *futuresExchangeInfoService) Do(ctx context.Context) (*binance.ExchangeInfo, error) {
	return s.svc.Do(ctx)
}

// futuresGetAccountService wraps futures.GetAccountService
type futuresGetAccountService struct {
	svc *futures.GetAccountService
}

func (s *futuresGetAccountService) Do(ctx context.Context) (*binance.Account, error) {
	return s.svc.Do(ctx)
}

// futuresCreateOrderService wraps futures.CreateOrderService
type futuresCreateOrderService struct {
	svc *futures.CreateOrderService
}

func (s *futuresCreateOrderService) Symbol(symbol string) CreateOrderService {
	s.svc.Symbol(symbol)
	return s
}

func (s *futuresCreateOrderService) Side(side string) CreateOrderService {
	s.svc.Side(futures.SideType(side))
	return s
}

func (s *futuresCreateOrderService) Type(orderType string) CreateOrderService {
	s.svc.Type(futures.OrderType(orderType))
	return s
}

func (s *futuresCreateOrderService) Quantity(quantity string) CreateOrderService {
	s.svc.Quantity(quantity)
	return s
}

func (s *futuresCreateOrderService) Price(price string) CreateOrderService {
	s.svc.Price(price)
	return s
}

func (s *futuresCreateOrderService) TimeInForce(timeInForce string) CreateOrderService {
	s.svc.TimeInForce(futures.TimeInForceType(timeInForce))
	return s
}

func (s *futuresCreateOrderService) NewOrderResponseType(newOrderResponseType string) CreateOrderService {
	s.svc.NewOrderResponseType(futures.NewOrderRespType(newOrderResponseType))
	return s
}

func (s *futuresCreateOrderService) Do(ctx context.Context) (*binance.CreateOrderResponse, error) {
	// Convert futures.CreateOrderResponse to binance.CreateOrderResponse
	resp, err := s.svc.Do(ctx)
	if err != nil {
		return nil, err
	}

	status := string(resp.Status)
	switch resp.Status {
	case "NEW":
		status = "NEW"
	case "FILLED":
		status = "FILLED"
	case "PARTIALLY_FILLED":
		status = "PARTIALLY_FILLED"
	case "CANCELED":
		status = "CANCELED"
	case "REJECTED":
		status = "REJECTED"
	case "EXPIRED":
		status = "EXPIRED"
	}

	orderType := string(resp.Type)
	switch resp.Type {
	case "LIMIT":
		orderType = "LIMIT"
	case "MARKET":
		orderType = "MARKET"
	case "STOP", "STOP_MARKET":
		orderType = "STOP_LOSS"
	case "TAKE_PROFIT", "TAKE_PROFIT_MARKET":
		orderType = "TAKE_PROFIT"
	case "LIMIT_MAKER":
		orderType = "LIMIT_MAKER"
	}

	tif := string(resp.TimeInForce)
	switch resp.TimeInForce {
	case "GTC":
		tif = "GTC"
	case "IOC":
		tif = "IOC"
	case "FOK":
		tif = "FOK"
	case "GTX":
		tif = "GTC" // Map GTX to GTC as it's not in spot API
	}

	result := &binance.CreateOrderResponse{
		Symbol:           resp.Symbol,
		OrderID:          resp.OrderID,
		ClientOrderID:    resp.ClientOrderID,
		Price:            resp.Price,
		OrigQuantity:     resp.OrigQuantity,
		ExecutedQuantity: resp.ExecutedQuantity,
		Status:           binance.OrderStatusType(status),
		TimeInForce:      binance.TimeInForceType(tif),
		Type:             binance.OrderType(orderType),
		Side:             binance.SideType(resp.Side),
	}

	reflVal := reflect.ValueOf(result).Elem()
	if cumQuote, err := strconv.ParseFloat(resp.CumQuote, 64); err == nil {
		reflVal.FieldByName("CumulativeQuoteQuantity").SetString(fmt.Sprintf("%.8f", cumQuote))
	}

	return result, nil
}

// futuresGetOrderService wraps futures.GetOrderService
type futuresGetOrderService struct {
	svc *futures.GetOrderService
}

func (s *futuresGetOrderService) Symbol(symbol string) GetOrderService {
	s.svc.Symbol(symbol)
	return s
}

func (s *futuresGetOrderService) OrderID(orderID string) GetOrderService {
	id, err := strconv.ParseInt(orderID, 10, 64)
	if err == nil {
		s.svc.OrderID(id)
	}
	return s
}

func (s *futuresGetOrderService) Do(ctx context.Context) (*binance.Order, error) {
	order, err := s.svc.Do(ctx)
	if err != nil {
		return nil, err
	}

	status := string(order.Status)
	switch order.Status {
	case "NEW":
		status = "NEW"
	case "FILLED":
		status = "FILLED"
	case "PARTIALLY_FILLED":
		status = "PARTIALLY_FILLED"
	case "CANCELED":
		status = "CANCELED"
	case "REJECTED":
		status = "REJECTED"
	case "EXPIRED":
		status = "EXPIRED"
	}

	orderType := string(order.Type)
	switch order.Type {
	case "LIMIT":
		orderType = "LIMIT"
	case "MARKET":
		orderType = "MARKET"
	case "STOP", "STOP_MARKET":
		orderType = "STOP_LOSS"
	case "TAKE_PROFIT", "TAKE_PROFIT_MARKET":
		orderType = "TAKE_PROFIT"
	case "LIMIT_MAKER":
		orderType = "LIMIT_MAKER"
	}

	tif := string(order.TimeInForce)
	switch order.TimeInForce {
	case "GTC":
		tif = "GTC"
	case "IOC":
		tif = "IOC"
	case "FOK":
		tif = "FOK"
	case "GTX":
		tif = "GTC" // Map GTX to GTC as it's not in spot API
	}

	result := &binance.Order{
		Symbol:           order.Symbol,
		OrderID:          order.OrderID,
		ClientOrderID:    order.ClientOrderID,
		Price:            order.Price,
		OrigQuantity:     order.OrigQuantity,
		ExecutedQuantity: order.ExecutedQuantity,
		Status:           binance.OrderStatusType(status),
		TimeInForce:      binance.TimeInForceType(tif),
		Type:             binance.OrderType(orderType),
		Side:             binance.SideType(order.Side),
		StopPrice:        order.StopPrice,
		Time:             order.Time,
		UpdateTime:       order.UpdateTime,
	}

	if cumQuote, err := strconv.ParseFloat(order.CumQuote, 64); err == nil {
		reflVal := reflect.ValueOf(result).Elem()
		reflVal.FieldByName("CumulativeQuoteQuantity").SetString(fmt.Sprintf("%.8f", cumQuote))
	}

	return result, nil
}

// Helper functions for string to number conversion
func parseOrderID(orderID string) int64 {
	v, _ := strconv.ParseInt(orderID, 10, 64)
	return v
}

func parseUint(s string) uint64 {
	v, _ := strconv.ParseUint(s, 10, 64)
	return v
}

// futuresCancelOrderService wraps futures.CancelOrderService
type futuresCancelOrderService struct {
	svc *futures.CancelOrderService
}

func (s *futuresCancelOrderService) Symbol(symbol string) CancelOrderService {
	s.svc.Symbol(symbol)
	return s
}

func (s *futuresCancelOrderService) OrderID(orderID string) CancelOrderService {
	s.svc.OrderID(mustParseInt64(orderID))
	return s
}

func (s *futuresCancelOrderService) Do(ctx context.Context) (*binance.CancelOrderResponse, error) {
	resp, err := s.svc.Do(ctx)
	if err != nil {
		return nil, err
	}

	// Map status from futures to spot
	status := string(resp.Status)
	switch resp.Status {
	case "NEW":
		status = "NEW"
	case "FILLED":
		status = "FILLED"
	case "PARTIALLY_FILLED":
		status = "PARTIALLY_FILLED"
	case "CANCELED":
		status = "CANCELED"
	case "REJECTED":
		status = "REJECTED"
	case "EXPIRED":
		status = "EXPIRED"
	}

	// Map order type
	orderType := string(resp.Type)
	switch resp.Type {
	case "LIMIT":
		orderType = "LIMIT"
	case "MARKET":
		orderType = "MARKET"
	case "STOP", "STOP_MARKET":
		orderType = "STOP_LOSS"
	case "TAKE_PROFIT", "TAKE_PROFIT_MARKET":
		orderType = "TAKE_PROFIT"
	case "LIMIT_MAKER":
		orderType = "LIMIT_MAKER"
	}

	// Map time in force
	tif := string(resp.TimeInForce)
	switch resp.TimeInForce {
	case "GTC":
		tif = "GTC"
	case "IOC":
		tif = "IOC"
	case "FOK":
		tif = "FOK"
	case "GTX":
		tif = "GTC" // Map GTX to GTC as it's not in spot API
	}

	// Create response with mapped fields
	result := &binance.CancelOrderResponse{
		Symbol:           resp.Symbol,
		OrderID:          resp.OrderID,
		ClientOrderID:    resp.ClientOrderID,
		Price:            resp.Price,
		OrigQuantity:     resp.OrigQuantity,
		ExecutedQuantity: resp.ExecutedQuantity,
		Status:           binance.OrderStatusType(status),
		TimeInForce:      binance.TimeInForceType(tif),
		Type:             binance.OrderType(orderType),
		Side:             binance.SideType(resp.Side),
		// Remove this line: StopPrice: resp.StopPrice,
		IcebergQuantity: resp.IcebergQuantity,
		UpdateTime:      resp.UpdateTime,
		IsWorking:       resp.IsWorking,
	}

	// Use reflection to set unexported fields if needed
	reflVal := reflect.ValueOf(result).Elem()
	if cumQuote, err := strconv.ParseFloat(resp.CumQuote, 64); err == nil {
		reflVal.FieldByName("CumulativeQuoteQuantity").SetString(fmt.Sprintf("%.8f", cumQuote))
	}

	return result, nil
}

// futuresListOpenOrdersService wraps futures.ListOpenOrdersService
type futuresListOpenOrdersService struct {
	svc *futures.ListOpenOrdersService
}

func (s *futuresListOpenOrdersService) Symbol(symbol string) ListOpenOrdersService {
	s.svc.Symbol(symbol)
	return s
}

func (s *futuresListOpenOrdersService) Do(ctx context.Context) ([]*binance.Order, error) {
	// Get the orders from the futures service
	futuresOrders, err := s.svc.Do(ctx)
	if err != nil {
		return nil, err
	}

	// Convert futures.Orders to binance.Orders
	var orders []*binance.Order
	for _, fo := range futuresOrders {
		// Convert string enums to their respective types
		status := binance.OrderStatusType(fo.Status)
		timeInForce := binance.TimeInForceType(fo.TimeInForce)
		orderType := binance.OrderType(fo.Type)
		side := binance.SideType(fo.Side)

		// Create the order with properly typed fields
		order := &binance.Order{
			Symbol:           fo.Symbol,
			OrderID:          fo.OrderID,
			ClientOrderID:    fo.ClientOrderID,
			Price:            fo.Price,
			OrigQuantity:     fo.OrigQuantity,
			ExecutedQuantity: fo.ExecutedQuantity,
			Status:           status,
			TimeInForce:      timeInForce,
			Type:             orderType,
			Side:             side,
			StopPrice:        fo.StopPrice,
			Time:             fo.Time,
			UpdateTime:       fo.UpdateTime,
			// Note: IsWorking and IsIsolated fields are not available in futures.Order
		}
		orders = append(orders, order)
	}

	return orders, nil
}

// mustParseInt64 converts a string to int64, panics on error
func mustParseInt64(s string) int64 {
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		panic(fmt.Sprintf("failed to parse int64: %v", err))
	}
	return i
}

// mustParseUint64 converts a string to uint64, panics on error
func mustParseUint64(s string) uint64 {
	i, err := strconv.ParseUint(s, 10, 64)
	if err != nil {
		panic(fmt.Sprintf("failed to parse uint64: %v", err))
	}
	return i
}
