package types

// BookEntry represents a single entry in the order book
// with price and quantity
// This is used for both bids and asks in the order book
type BookEntry struct {
	// Price of the order book entry
	Price float64 `json:"price"`
	// Quantity of the order book entry
	Quantity float64 `json:"quantity"`
}

// OrderBook represents a snapshot of the order book with
// bids (buy orders) and asks (sell orders)
type OrderBook struct {
	// LastUpdateID is the last update ID from the exchange
	LastUpdateID int64 `json:"lastUpdateId"`
	// Bids are the buy orders in the order book
	Bids []BookEntry `json:"bids"`
	// Asks are the sell orders in the order book
	Asks []BookEntry `json:"asks"`
	// Time when the order book was last updated (in milliseconds since epoch)
	Time int64 `json:"time,omitempty"`
}
