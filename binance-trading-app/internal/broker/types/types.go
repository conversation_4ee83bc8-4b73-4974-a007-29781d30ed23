package types

import "context"

// Asset represents a trading asset/currency
type Asset string

// BrokerType определяет тип брокера
type BrokerType string

const (
	// BrokerTypeBinance представляет Binance брокера
	BrokerTypeBinance BrokerType = "binance"
)

// Symbol представляет торговую пару
type Symbol struct {
	ID           string  `json:"id"`
	BaseAsset    string  `json:"base_asset"`
	QuoteAsset   string  `json:"quote_asset"`
	MinPrice     float64 `json:"min_price"`
	MaxPrice     float64 `json:"max_price"`
	TickSize     float64 `json:"tick_size"`
	MinQty       float64 `json:"min_qty"`
	MaxQty       float64 `json:"max_qty"`
	StepSize     float64 `json:"step_size"`
	MinNotional  float64 `json:"min_notional"`
}

// Balance представляет баланс актива
type Balance struct {
	Asset  string  `json:"asset"`
	Free   float64 `json:"free"`
	Locked float64 `json:"locked"`
}

// Candle представляет свечу
type Candle struct {
	OpenTime  int64   `json:"open_time"`
	Open      float64 `json:"open"`
	High      float64 `json:"high"`
	Low       float64 `json:"low"`
	Close     float64 `json:"close"`
	Volume    float64 `json:"volume"`
	CloseTime int64   `json:"close_time"`
}

// Order представляет ордер
type Order struct {
	ID            string  `json:"id"`
	Symbol        string  `json:"symbol"`
	Side          string  `json:"side"`      // "BUY" или "SELL"
	Type          string  `json:"type"`      // "LIMIT", "MARKET" и т.д.
	Price         float64 `json:"price"`
	Quantity      float64 `json:"quantity"`
	ExecutedQty   float64 `json:"executed_qty"`
	Status        string  `json:"status"`
	TimeInForce   string  `json:"time_in_force"`
	CreatedAt     int64   `json:"created_at"`
	UpdatedAt     int64   `json:"updated_at"`
	LastCheckTime int64   `json:"last_check_time"` // Временная метка последней проверки на исполнение
}

// ExchangeType определяет тип биржи
type ExchangeType string

const (
	// ExchangeTypeSpot представляет спотовую торговлю
	ExchangeTypeSpot ExchangeType = "spot"
	// ExchangeTypeFutures представляет торговлю фьючерсами
	ExchangeTypeFutures ExchangeType = "futures"
)

// Config содержит конфигурацию для создания брокера
type Config struct {
	APIKey       string       `json:"api_key,omitempty"`
	APISecret    string       `json:"api_secret,omitempty"`
	TestNet      bool         `json:"test_net,omitempty"`
	TestMode     bool         `json:"test_mode,omitempty"` // When true, use simulated/test environment
	ExchangeType ExchangeType `json:"exchange_type,omitempty"`
}

// Broker определяет общий интерфейс для работы с брокерами
type Broker interface {
	// Подключение и аутентификация
	Connect(ctx context.Context) error
	IsConnected() bool

	// Установка учетных данных API
	SetAPIKey(apiKey, apiSecret string, isTestnet bool) error

	// Информация о бирже
	GetExchangeInfo(ctx context.Context) ([]Symbol, error)

	// Работа с аккаунтом
	GetAccountInfo(ctx context.Context) ([]Balance, error)
	GetBalance(ctx context.Context, asset string) (*Balance, error)

	// Работа с ордерами
	CreateOrder(ctx context.Context, symbol, side, orderType string, quantity, price float64) (*Order, error)
	GetOrder(ctx context.Context, orderID, symbol string) (*Order, error)
	CancelOrder(ctx context.Context, orderID, symbol string) error
	GetOpenOrders(ctx context.Context, symbol string) ([]*Order, error)

	// Подписка на рыночные данные
	SubscribeToCandles(symbol, interval string) (chan []Candle, error) // interval: "1m", "5m", "1h" и т.д.
	SubscribeToOrderBook(symbol string) (chan OrderBook, error)

	// Закрытие соединения
	Close() error
}
