package broker

import (
	"sync"
)

// brokerRegistry хранит зарегистрированные фабричные функции брокеров
var (
	brokerRegistry   = make(map[BrokerType]func(Config) (Broker, error))
	registryMutex    sync.RWMutex
)

// Register регистрирует фабричную функцию для типа брокера
func Register(brokerType BrokerType, factory func(Config) (Broker, error)) {
	registryMutex.Lock()
	defer registryMutex.Unlock()
	brokerRegistry[brokerType] = factory
}

// NewBroker создает новый экземпляр брокера указанного типа
func NewBroker(brokerType BrokerType, config Config) (Broker, error) {
	registryMutex.RLock()
	factory, exists := brokerRegistry[brokerType]
	registryMutex.RUnlock()

	if !exists {
		return nil, ErrUnsupportedBrokerType
	}

	return factory(config)
}
