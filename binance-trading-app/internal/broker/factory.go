package broker

import (
	"fmt"

	"binance-trading-app/internal/broker/test"
	"binance-trading-app/internal/broker/types"
)

// BrokerFactory создает экземпляры брокеров
type BrokerFactory struct{}


// NewBrokerFactory создает новую фабрику брокеров
func NewBrokerFactory() *BrokerFactory {
	return &BrokerFactory{}
}

// CreateBroker создает новый экземпляр брокера указанного типа
func (f *BrokerFactory) CreateBroker(brokerType types.BrokerType, config types.Config) (Broker, error) {
	switch brokerType {
	case BrokerTypeBinance:
		return f.createBinanceBroker(config)
	default:
		return nil, fmt.Errorf("unsupported broker type: %s", brokerType)
	}
}

// createBinanceBroker создает новый экземпляр Binance брокера
func (f *BrokerFactory) createBinanceBroker(config Config) (Broker, error) {
	// Если API ключи не указаны, создаем тестового брокера
	if config.APIKey == "" || config.APISecret == "" {
		return test.NewTestBroker(), nil
	}

	// Используем рефлексию для создания экземпляра без прямого импорта
	brokerType, ok := brokerTypes[BrokerTypeBinance]
	if !ok {
		return nil, fmt.Errorf("broker type not registered: %s", BrokerTypeBinance)
	}

	broker, ok := brokerType.(func(Config) (Broker, error))
	if !ok {
		return nil, fmt.Errorf("invalid broker type: %T", brokerType)
	}

	return broker(config)
}

// RegisterBroker регистрирует фабричную функцию для типа брокера
func (f *BrokerFactory) RegisterBroker(brokerType types.BrokerType, factoryFn interface{}) {
	brokerTypes[brokerType] = factoryFn
}

// brokerTypes хранит зарегистрированные фабричные функции
var brokerTypes = make(map[types.BrokerType]interface{})
