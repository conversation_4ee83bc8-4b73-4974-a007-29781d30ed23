package storage

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"
)

// APIKey represents an exchange API key in the database
type APIKey struct {
	ID              int64     `json:"id"`
	Exchange        string    `json:"exchange"`
	Name           string    `json:"name"`
	APIKey         string    `json:"api_key,omitempty"`         // Only used for retrieval
	APISecret      string    `json:"api_secret,omitempty"`      // Only used for retrieval
	APIKeyEncrypted string    `json:"-"`
	APISecretEncrypted string  `json:"-"`
	IsTestnet      bool      `json:"is_testnet"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// SaveAPIKey securely stores an API key in the database
func (s *Storage) SaveAPIKey(ctx context.Context, key *APIKey) error {
	if key == nil {
		return errors.New("api key cannot be nil")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	// Encrypt sensitive data
	keyEncrypted, err := s.crypto.Encrypt([]byte(key.APIKey))
	if err != nil {
		return fmt.Errorf("failed to encrypt API key: %w", err)
	}

	secretEncrypted, err := s.crypto.Encrypt([]byte(key.APISecret))
	if err != nil {
		return fmt.Errorf("failed to encrypt API secret: %w", err)
	}

	// Use a transaction to ensure data consistency
	return s.WithTx(ctx, func(tx *sql.Tx) error {
		// Check if the key already exists
		var exists bool
		err := tx.QueryRowContext(ctx, 
			"SELECT EXISTS(SELECT 1 FROM api_keys WHERE exchange = ? AND name = ?)",
			key.Exchange, key.Name,
		).Scan(&exists)
		if err != nil {
			return fmt.Errorf("failed to check if key exists: %w", err)
		}

		if exists {
			// Update existing key
			_, err = tx.ExecContext(ctx, `
				UPDATE api_keys 
				SET api_key_encrypted = ?, 
				    api_secret_encrypted = ?,
				    is_testnet = ?,
				    updated_at = CURRENT_TIMESTAMP
				WHERE exchange = ? AND name = ?
			`, keyEncrypted, secretEncrypted, key.IsTestnet, key.Exchange, key.Name)
		} else {
			// Insert new key
			_, err = tx.ExecContext(ctx, `
				INSERT INTO api_keys (
					exchange, name, api_key_encrypted, 
					api_secret_encrypted, is_testnet
				) VALUES (?, ?, ?, ?, ?)
			`, key.Exchange, key.Name, keyEncrypted, secretEncrypted, key.IsTestnet)
		}

		if err != nil {
			return fmt.Errorf("failed to save API key: %w", err)
		}

		return nil
	})
}

// GetAPIKey retrieves an API key by exchange and name
func (s *Storage) GetAPIKey(ctx context.Context, exchange, name string) (*APIKey, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var key APIKey
	var keyEncrypted, secretEncrypted string

	err := s.db.QueryRowContext(ctx, `
		SELECT 
			id, exchange, name, 
			api_key_encrypted, api_secret_encrypted, 
			is_testnet, created_at, updated_at
		FROM api_keys 
		WHERE exchange = ? AND name = ?
	`, exchange, name).Scan(
		&key.ID, &key.Exchange, &key.Name,
		&keyEncrypted, &secretEncrypted,
		&key.IsTestnet, &key.CreatedAt, &key.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("API key not found: %s/%s", exchange, name)
		}
		return nil, fmt.Errorf("failed to get API key: %w", err)
	}

	// Decrypt the sensitive data
	keyBytes, err := s.crypto.Decrypt(keyEncrypted)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt API key: %w", err)
	}

	secretBytes, err := s.crypto.Decrypt(secretEncrypted)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt API secret: %w", err)
	}

	key.APIKey = string(keyBytes)
	key.APISecret = string(secretBytes)

	return &key, nil
}

// ListAPIKeys returns a list of all API keys for an exchange
func (s *Storage) ListAPIKeys(ctx context.Context, exchange string) ([]*APIKey, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	rows, err := s.db.QueryContext(ctx, `
		SELECT 
			id, exchange, name, 
			is_testnet, created_at, updated_at
		FROM api_keys 
		WHERE exchange = ?
		ORDER BY name
	`, exchange)
	if err != nil {
		return nil, fmt.Errorf("failed to list API keys: %w", err)
	}
	defer rows.Close()

	var keys []*APIKey
	for rows.Next() {
		var key APIKey
		if err := rows.Scan(
			&key.ID, &key.Exchange, &key.Name,
			&key.IsTestnet, &key.CreatedAt, &key.UpdatedAt,
		); err != nil {
			return nil, fmt.Errorf("failed to scan API key: %w", err)
		}
		keys = append(keys, &key)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating API keys: %w", err)
	}

	return keys, nil
}

// DeleteAPIKey removes an API key from the database
func (s *Storage) DeleteAPIKey(ctx context.Context, exchange, name string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	result, err := s.db.ExecContext(ctx, 
		"DELETE FROM api_keys WHERE exchange = ? AND name = ?", 
		exchange, name,
	)
	if err != nil {
		return fmt.Errorf("failed to delete API key: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("API key not found: %s/%s", exchange, name)
	}

	return nil
}

// TestConnection tests if the API key is valid by making a test API call
func (s *Storage) TestConnection(ctx context.Context, key *APIKey) (bool, error) {
	// This is a placeholder for actual API testing logic
	// In a real implementation, you would make a test API call to the exchange
	// and verify the credentials are valid.

	// For now, we'll just check if the key and secret are not empty
	if key.APIKey == "" || key.APISecret == "" {
		return false, errors.New("API key and secret cannot be empty")
	}

	// TODO: Implement actual API test call
	return true, nil
}
