package storage

// Config holds storage configuration
type Config struct {
	// Path to the SQLite database file
	DBPath string

	// Encryption key for sensitive data
	// In production, this should be securely retrieved from a key management system
	EncryptionKey string

	// WAL (Write-Ahead Logging) mode for SQLite
	// Improves performance and allows for better concurrency
	WALMode bool
}

// DefaultConfig returns a default configuration
func DefaultConfig() *Config {
	return &Config{
		DBPath:        "data/app.db",
		EncryptionKey: "default-insecure-key-change-me",
		WALMode:       true,
	}
}

// WithDBPath sets the database path
func (c *Config) WithDBPath(path string) *Config {
	c.DBPath = path
	return c
}

// WithEncryptionKey sets the encryption key
func (c *Config) WithEncryptionKey(key string) *Config {
	c.EncryptionKey = key
	return c
}

// WithWALMode enables or disables WAL mode
func (c *Config) WithWALMode(enabled bool) *Config {
	c.WALMode = enabled
	return c
}
