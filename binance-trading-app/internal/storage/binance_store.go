package storage

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/adshao/go-binance/v2"
)

// BinanceStore handles Binance-specific storage operations
type BinanceStore struct {
	storage *Storage
}

// NewBinanceStore creates a new BinanceStore instance
func NewBinanceStore(storage *Storage) *BinanceStore {
	return &BinanceStore{
		storage: storage,
	}
}

// SaveAPIKey saves a Binance API key to the storage
func (s *BinanceStore) SaveAPIKey(ctx context.Context, name, apiKey, apiSecret string, isTestnet bool) error {
	return s.storage.SaveAPIKey(ctx, &APIKey{
		Exchange:   "binance",
		Name:       name,
		APIKey:     apiKey,
		APISecret:  apiSecret,
		IsTestnet:  isTestnet,
	})
}

// GetBinanceClient retrieves a configured Binance client from storage
func (s *BinanceStore) GetBinanceClient(ctx context.Context, name string) (*binance.Client, bool, error) {
	key, err := s.storage.GetAPIKey(ctx, "binance", name)
	if err != nil {
		return nil, false, fmt.Errorf("failed to get API key: %w", err)
	}

	// Create a new Binance client
	client := binance.NewClient(key.APIKey, key.APISecret)
	if key.IsTestnet {
		binance.UseTestnet = true
	}

	return client, key.IsTestnet, nil
}

// SaveStrategy saves a trading strategy to the database
func (s *BinanceStore) SaveStrategy(ctx context.Context, name, description string, config interface{}) error {
	configJSON, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal strategy config: %w", err)
	}

	configEncrypted, err := s.storage.crypto.Encrypt(configJSON)
	if err != nil {
		return fmt.Errorf("failed to encrypt strategy config: %w", err)
	}

	return s.storage.WithTx(ctx, func(tx *sql.Tx) error {
		// Check if strategy exists
		var exists bool
		err := tx.QueryRowContext(ctx, 
			"SELECT EXISTS(SELECT 1 FROM strategies WHERE name = ?)",
			name,
		).Scan(&exists)

		if err != nil {
			return fmt.Errorf("failed to check if strategy exists: %w", err)
		}

		var result sql.Result
		if exists {
			// Update existing strategy
			result, err = tx.ExecContext(ctx, `
				UPDATE strategies 
				set description = ?,
				    config_encrypted = ?,
				    updated_at = CURRENT_TIMESTAMP
				WHERE name = ?
			`, description, configEncrypted, name)
		} else {
			// Insert new strategy
			result, err = tx.ExecContext(ctx, `
				INSERT INTO strategies (
					name, description, config_encrypted
				) VALUES (?, ?, ?)
			`, name, description, configEncrypted)
		}

		if err != nil {
			return fmt.Errorf("failed to save strategy: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			return fmt.Errorf("no rows affected when saving strategy")
		}

		return nil
	})
}

// GetStrategy retrieves a strategy by name
func (s *BinanceStore) GetStrategy(ctx context.Context, name string, config interface{}) error {
	var configEncrypted string
	err := s.storage.db.QueryRowContext(ctx, `
		SELECT config_encrypted 
		FROM strategies 
		WHERE name = ?
	`, name).Scan(&configEncrypted)

	if err != nil {
		return fmt.Errorf("failed to get strategy: %w", err)
	}

	configJSON, err := s.storage.crypto.Decrypt(configEncrypted)
	if err != nil {
		return fmt.Errorf("failed to decrypt strategy config: %w", err)
	}

	if err := json.Unmarshal(configJSON, config); err != nil {
		return fmt.Errorf("failed to unmarshal strategy config: %w", err)
	}

	return nil
}

// ListStrategies returns a list of all strategies
func (s *BinanceStore) ListStrategies(ctx context.Context) ([]string, error) {
	rows, err := s.storage.db.QueryContext(ctx, `
		SELECT name 
		FROM strategies 
		ORDER BY name
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to list strategies: %w", err)
	}
	defer rows.Close()

	var strategies []string
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			return nil, fmt.Errorf("failed to scan strategy name: %w", err)
		}
		strategies = append(strategies, name)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating strategies: %w", err)
	}

	return strategies, nil
}
