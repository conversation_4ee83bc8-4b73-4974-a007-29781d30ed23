package storage

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	_ "github.com/mattn/go-sqlite3"
)

// Storage handles all database operations
type Storage struct {
	db     *sql.DB
	crypto *Crypto
	config *Config
	mu     sync.RWMutex
}

// New creates a new Storage instance
func New(config *Config) (*Storage, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// Create the data directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(config.DBPath), 0755); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	// Initialize the database
	db, err := sql.Open("sqlite3", config.DBPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Enable WAL mode if configured
	if config.WALMode {
		if _, err := db.Exec("PRAGMA journal_mode=WAL;"); err != nil {
			return nil, fmt.Errorf("failed to enable WAL mode: %w", err)
		}
	}

	// Initialize crypto
	crypto, err := NewCrypto(config.EncryptionKey)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize crypto: %w", err)
	}

	s := &Storage{
		db:     db,
		crypto: crypto,
		config: config,
	}

	// Create tables if they don't exist
	if err := s.migrate(); err != nil {
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	return s, nil
}

// Close closes the database connection
func (s *Storage) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.db != nil {
		return s.db.Close()
	}
	return nil
}

// migrate creates the necessary database tables
func (s *Storage) migrate() error {
	tx, err := s.db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Create API keys table
	_, err = tx.Exec(`
		CREATE TABLE IF NOT EXISTS api_keys (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			exchange TEXT NOT NULL,
			name TEXT NOT NULL,
			api_key_encrypted TEXT NOT NULL,
			api_secret_encrypted TEXT NOT NULL,
			is_testnet BOOLEAN NOT NULL DEFAULT 0,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(exchange, name)
		);
	`)
	if err != nil {
		return fmt.Errorf("failed to create api_keys table: %w", err)
	}

	// Create trades table
	_, err = tx.Exec(`
		CREATE TABLE IF NOT EXISTS trades (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			exchange_id TEXT NOT NULL,
			trade_id TEXT NOT NULL,
			symbol TEXT NOT NULL,
			side TEXT NOT NULL,
			price REAL NOT NULL,
			quantity REAL NOT NULL,
			status TEXT NOT NULL,
			timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
			metadata_encrypted TEXT,
			UNIQUE(exchange_id, trade_id)
		);
	`)
	if err != nil {
		return fmt.Errorf("failed to create trades table: %w", err)
	}

	// Create strategies table
	_, err = tx.Exec(`
		CREATE TABLE IF NOT EXISTS strategies (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT NOT NULL,
			description TEXT,
			config_encrypted TEXT NOT NULL,
			is_active BOOLEAN NOT NULL DEFAULT 1,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(name)
		);
	`)
	if err != nil {
		return fmt.Errorf("failed to create strategies table: %w", err)
	}

	return tx.Commit()
}

// WithTx executes a function within a database transaction
func (s *Storage) WithTx(ctx context.Context, fn func(*sql.Tx) error) error {
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	if err := fn(tx); err != nil {
		if rbErr := tx.Rollback(); rbErr != nil {
			return fmt.Errorf("tx err: %v, rb err: %w", err, rbErr)
		}
		return err
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}
