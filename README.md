# Binance Trading Desktop App

Десктопное приложение для управления торговыми операциями на бирже Binance с возможностью автоматизации торговых стратегий.

## 🚀 Технологический стек

- **Frontend**: Vue 3 + TypeScript + Composition API
- **Backend**: Go (с использованием Wails)
- **UI Framework**: Vuetify 3 или Quasar (на выбор)
- **API**: Binance API (REST и WebSocket)
- **База данных**: SQLite (для хранения настроек и истории)
- **Дополнительно**: 
  - WebSocket для реального обновления данных
  - TypeScript для типизации
  - ESLint + Prettier для форматирования кода

## 📋 Основной функционал

### Управление аккаунтом
- Просмотр баланса и истории операций
- Управление ордерами (лимитные, рыночные, стоп-лосс и т.д.)
- Просмотр открытых позиций

### Торговля
- Графики цен с индикаторами
- Быстрая торговля (покупка/продажа)
- Расширенный торговый интерфейс

### Автоматизация
- Визуальный конструктор стратегий
- Backtesting торговых стратегий
- Автоматическое исполнение стратегий

### Аналитика
- История сделок
- Анализ эффективности торговли
- Экспорт отчетов

## 🛠 Установка и запуск

1. Установите зависимости:
```bash
# Клонируйте репозиторий
git clone [your-repo-url]
cd binance-trading-app

# Установите зависимости Go
cd backend
go mod download

# Установите зависимости Node.js
cd ../frontend
npm install
```

2. Настройте API ключи Binance в конфигурационном файле

3. Запустите приложение:
```bash
# В корневой директории проекта
wails dev
```

## 📄 Лицензия

MIT

## 📞 Контакты

Разработано [Ваше имя/команда] - [ваш email]

---

*Этот проект не связан с Binance. Используйте на свой страх и риск. Торговля на бирже связана с финансовыми рисками.*
